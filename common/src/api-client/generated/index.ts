// This file is auto-generated by generate-api-client-no-schemas.js
// It re-exports all API clients and types

// Import and export axios instance and custom instance
import { AXIOS_INSTANCE, customInstance } from '../mutator/custom-instance';
export { AXIOS_INSTANCE, customInstance };

// Import and export all types
import * as ApiTypes from './types';
export * from './types';

// Import API modules
import { getAuth } from './auth/auth';
import { getSms } from './sms/sms';
import { getUsers } from './users/users';

// Create API classes
export class AuthApi {
  private client: typeof customInstance;

  constructor(client = customInstance) {
    this.client = client;
  }

  private api = getAuth();

  /**
   * Login with credentials
   */
  login(loginDto: ApiTypes.LoginDto) {
    return this.api.authControllerLogin(loginDto);
  }

  /**
   * Refresh access token
   */
  refreshToken(refreshTokenDto: ApiTypes.RefreshTokenDto) {
    return this.api.authControllerRefreshToken(refreshTokenDto);
  }

}

export class SmsApi {
  private client: typeof customInstance;

  constructor(client = customInstance) {
    this.client = client;
  }

  private api = getSms();

  /**
   * 获取图形验证码
   */
  getCaptcha() {
    return this.api.smsControllerGetCaptcha();
  }

  /**
   * 发送短信验证码
   */
  sendSmsCode(sendSmsDto: ApiTypes.SendSmsDto) {
    return this.api.smsControllerSendSmsCode(sendSmsDto);
  }

  /**
   * 验证短信验证码
   */
  verifySmsCode(verifySmsDto: ApiTypes.VerifySmsDto) {
    return this.api.smsControllerVerifySmsCode(verifySmsDto);
  }

}

export class UsersApi {
  private client: typeof customInstance;

  constructor(client = customInstance) {
    this.client = client;
  }

  private api = getUsers();

  /**
   * Query users with filters and pagination
   */
  findAll(params?: ApiTypes.UserControllerFindAllParams) {
    return this.api.userControllerFindAll(params);
  }

  /**
   * Get a user by ID
   */
  findOne(id: string) {
    return this.api.userControllerFindOne(id);
  }

}

// Export API client instances
export const apiClients = {
  auth: new AuthApi(),
  sms: new SmsApi(),
  users: new UsersApi(),
};
