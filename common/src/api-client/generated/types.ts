// This file is auto-generated by generate-api-client-no-schemas.js
// It exports all types from common/src that are used in the API client

// Define types that are not in the project but are in the OpenAPI spec
export interface SendSmsDto {
  phone?: string;
  captcha?: string;
}

export interface VerifySmsDto {
  phone?: string;
  code?: string;
}

export { IUser } from '../../interfaces/user.interface';
export { IUserAuth } from '../../interfaces/user-auth.interface';
export { JwtPayload, AuthResponse, ThirdPartyUserInfo } from '../../interfaces/auth.interface';
export { UserStatus } from '../../enums/user-status.enum';
export { LoginType } from '../../enums/login-type.enum';
export { AuthType } from '../../enums/auth-type.enum';
export { IUserQueryDto } from '../../dto/user-query.dto';
export { DictionaryValueDto, CreateDictionaryEntryDto, UpdateDictionaryEntryDto, DictionaryQueryDto, DictionaryEntryDto } from '../../dto/dictionary.dto';
export { CreateDepartmentDto, UpdateDepartmentDto, DepartmentDto, DepartmentTreeDto, DepartmentQueryDto } from '../../dto/department.dto';
export { ILoginDto, IRefreshTokenDto } from '../../dto/auth.dto';

// 创建类型别名，将接口映射到没有 'I' 前缀的名称
// 先从当前文件导入
import { IUser, IUserAuth, IUserQueryDto, ILoginDto, IRefreshTokenDto } from '.';

export type User = IUser;
export type UserAuth = IUserAuth;
export type UserQueryDto = IUserQueryDto;
export type LoginDto = ILoginDto;
export type RefreshTokenDto = IRefreshTokenDto;


// 为 OpenAPI 规范中的类型创建别名
export type UserControllerFindAllParams = IUserQueryDto;
