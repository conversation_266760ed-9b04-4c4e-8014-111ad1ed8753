/**
 * Generated by orval v7.8.0 🍺
 * Do not edit manually.
 * Mini Shop API
 * The Mini Shop API documentation
 * OpenAPI spec version: 1.0
 */
import type { UserControllerFindAllParams  } from "../types";

import { customInstance } from "../../mutator/custom-instance";

export const getUsers = () => {
  /**
   * @summary Query users with filters and pagination
   */
  const userControllerFindAll = (params?: UserControllerFindAllParams) => {
    return customInstance<void>({ url: `/api/users`, method: "GET", params });
  };
  /**
   * @summary Get a user by ID
   */
  const userControllerFindOne = (id: string) => {
    return customInstance<void>({ url: `/api/users/${id}`, method: "GET" });
  };
  return { userControllerFindAll, userControllerFindOne };
};

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

export type UserControllerFindAllResult = NonNullable<
  Awaited<ReturnType<ReturnType<typeof getUsers>["userControllerFindAll"]>>
>;
export type UserControllerFindOneResult = NonNullable<
  Awaited<ReturnType<ReturnType<typeof getUsers>["userControllerFindOne"]>>
>;
