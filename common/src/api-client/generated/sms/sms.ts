/**
 * Generated by orval v7.8.0 🍺
 * Do not edit manually.
 * Mini Shop API
 * The Mini Shop API documentation
 * OpenAPI spec version: 1.0
 */
import type {
  SendSmsDto,

  VerifySmsDto,
 } from "../types";

import { customInstance } from "../../mutator/custom-instance";
import {CaptchaResponseDto, ResponseDto, SmsResponseDto} from "../../../../../backend/src/dto/response.dto";

export const getSms = () => {
  /**
   * @summary 获取图形验证码
   */
  const smsControllerGetCaptcha = () => {
    return customInstance<ResponseDto<CaptchaResponseDto>>({
      url: `/api/sms/captcha`,
      method: "GET",
    });
  };
  /**
   * @summary 发送短信验证码
   */
  const smsControllerSendSmsCode = (sendSmsDto: SendSmsDto) => {
    return customInstance<SmsResponseDto>({
      url: `/api/sms/send`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: sendSmsDto,
    });
  };
  /**
   * @summary 验证短信验证码
   */
  const smsControllerVerifySmsCode = (verifySmsDto: VerifySmsDto) => {
    return customInstance<SmsResponseDto>({
      url: `/api/sms/verify`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: verifySmsDto,
    });
  };
  return {
    smsControllerGetCaptcha,
    smsControllerSendSmsCode,
    smsControllerVerifySmsCode,
  };
};

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

export type SmsControllerGetCaptchaResult = NonNullable<
  Awaited<ReturnType<ReturnType<typeof getSms>["smsControllerGetCaptcha"]>>
>;
export type SmsControllerSendSmsCodeResult = NonNullable<
  Awaited<ReturnType<ReturnType<typeof getSms>["smsControllerSendSmsCode"]>>
>;
export type SmsControllerVerifySmsCodeResult = NonNullable<
  Awaited<ReturnType<ReturnType<typeof getSms>["smsControllerVerifySmsCode"]>>
>;
