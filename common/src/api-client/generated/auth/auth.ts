/**
 * Generated by orval v7.8.0 🍺
 * Do not edit manually.
 * Mini Shop API
 * The Mini Shop API documentation
 * OpenAPI spec version: 1.0
 */
import type { LoginDto, RefreshTokenDto  } from "../types";

import { customInstance } from "../../mutator/custom-instance";

export const getAuth = () => {
  /**
   * @summary Login with credentials
   */
  const authControllerLogin = (loginDto: LoginDto) => {
    return customInstance<void>({
      url: `/api/auth/login`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: loginDto,
    });
  };
  /**
   * @summary Refresh access token
   */
  const authControllerRefreshToken = (refreshTokenDto: RefreshTokenDto) => {
    return customInstance<void>({
      url: `/api/auth/refresh-token`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: refreshTokenDto,
    });
  };
  return { authControllerLogin, authControllerRefreshToken };
};

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

export type AuthControllerLoginResult = NonNullable<
  Awaited<ReturnType<ReturnType<typeof getAuth>["authControllerLogin"]>>
>;
export type AuthControllerRefreshTokenResult = NonNullable<
  Awaited<ReturnType<ReturnType<typeof getAuth>["authControllerRefreshToken"]>>
>;
