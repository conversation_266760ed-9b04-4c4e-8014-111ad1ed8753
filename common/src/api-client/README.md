# API 客户端

这个目录包含了用于与后端 API 通信的 API 客户端。客户端是根据 OpenAPI 规范自动生成的，并使用 common/src 目录中的现有 DTO。

## 使用方法

### 导入 API 客户端

```typescript
// 导入默认客户端（包含所有 API）
import apiClient from '@common/src/api-client/generated';

// 或者导入特定的 API
import { authApi, usersApi } from '@common/src/api-client/generated';

// 或者导入 API 类来创建自定义实例
import { AuthApi, UsersApi } from '@common/src/api-client/generated';
```

### 身份验证

```typescript
import apiClient from '@common/src/api-client/generated';
import { AuthType } from '@common/src/api-client/generated';

// 登录
async function login(username: string, password: string) {
  try {
    const response = await apiClient.auth.login({
      account: username,
      authCredential: password,
      authType: AuthType.PASSWORD
    });
    
    // 存储令牌
    localStorage.setItem('accessToken', response.accessToken);
    localStorage.setItem('refreshToken', response.refreshToken);
    
    return response.user;
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
}

// 刷新令牌
async function refreshToken(refreshToken: string, token: string) {
  try {
    const response = await apiClient.auth.refreshToken({
      refreshToken,
      token
    });
    
    // 更新令牌
    localStorage.setItem('accessToken', response.accessToken);
    localStorage.setItem('refreshToken', response.refreshToken);
    
    return response;
  } catch (error) {
    console.error('令牌刷新失败:', error);
    throw error;
  }
}
```

### 用户管理

```typescript
import apiClient from '@common/src/api-client/generated';

// 获取所有用户
async function getAllUsers(query = {}) {
  try {
    const users = await apiClient.users.findAll(query);
    return users;
  } catch (error) {
    console.error('获取用户失败:', error);
    throw error;
  }
}

// 根据 ID 获取用户
async function getUserById(id) {
  try {
    const user = await apiClient.users.findOne(id);
    return user;
  } catch (error) {
    console.error(`获取用户 ${id} 失败:`, error);
    throw error;
  }
}
```

### 短信验证

```typescript
import apiClient from '@common/src/api-client/generated';

// 获取验证码
async function getCaptcha() {
  try {
    return await apiClient.sms.getCaptcha();
  } catch (error) {
    console.error('获取验证码失败:', error);
    throw error;
  }
}

// 发送短信验证码
async function sendSmsCode(phone, captcha) {
  try {
    return await apiClient.sms.sendSmsCode({
      phone,
      captcha
    });
  } catch (error) {
    console.error('发送短信验证码失败:', error);
    throw error;
  }
}

// 验证短信验证码
async function verifySmsCode(phone, code) {
  try {
    return await apiClient.sms.verifySmsCode({
      phone,
      code
    });
  } catch (error) {
    console.error('验证短信验证码失败:', error);
    throw error;
  }
}
```

### 自定义 API 客户端

你可以创建一个具有不同基本 URL 或自定义配置的自定义 API 客户端：

```typescript
import { AuthApi, customInstance } from '@common/src/api-client/generated';

// 创建一个具有不同基本 URL 的自定义实例
const customAxios = customInstance.create({
  baseURL: 'https://api.example.com',
});

// 创建一个自定义 API 客户端
const customAuthApi = new AuthApi(customAxios);

// 使用自定义 API 客户端
const response = await customAuthApi.login({
  account: 'username',
  authCredential: 'password',
  authType: AuthType.PASSWORD
});
```

## 重新生成 API 客户端

在 OpenAPI 规范或 DTO 发生更改后，要重新生成 API 客户端：

```bash
npm run generate:api:no-schemas
```

这将：
1. 根据 OpenAPI 规范生成 API 客户端
2. 使用 common/src 目录中的现有 DTO
3. 创建一个干净、文档完善的 API 客户端接口
