import { IsString, IsOptional, IsBoolean, IsNumber, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Create Product Category DTO
 */
export class CreateProductCategoryDto {
  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  image?: string;

  @IsNumber()
  @IsOptional()
  orderNum?: number;

  @IsNumber()
  @IsOptional()
  parentId?: number;

  @IsBoolean()
  @IsOptional()
  status?: boolean;
}

/**
 * Update Product Category DTO
 */
export class UpdateProductCategoryDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  image?: string;

  @IsNumber()
  @IsOptional()
  orderNum?: number;

  @IsNumber()
  @IsOptional()
  parentId?: number;

  @IsBoolean()
  @IsOptional()
  status?: boolean;
}

/**
 * Product Category Query DTO
 */
export class ProductCategoryQueryDto {
  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;

  @IsString()
  @IsOptional()
  name?: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;

  @IsNumber()
  @IsOptional()
  parentId?: number;

  @IsString()
  @IsOptional()
  sortBy?: string;

  @IsString()
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Product Category Tree DTO
 */
export class ProductCategoryTreeDto {
  id: number;
  name: string;
  description?: string;
  image?: string;
  orderNum: number;
  parentId?: number;
  status: boolean;
  children?: ProductCategoryTreeDto[];
  productCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Product Category DTO
 */
export class ProductCategoryDto {
  id: number;
  name: string;
  description?: string;
  image?: string;
  orderNum: number;
  parentId?: number;
  status: boolean;
  createdAt: Date;
  updatedAt: Date;
}
