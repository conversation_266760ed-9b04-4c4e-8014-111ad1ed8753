import { IsString, IsOptional, IsBoolean, IsNumber, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Dictionary Value DTO for creation and updates
 */
export class DictionaryValueDto {
  @IsNumber()
  @IsOptional()
  id?: number;

  @IsString()
  value: string;

  @IsString()
  @IsOptional()
  label?: string;

  @IsNumber()
  @IsOptional()
  orderNum?: number;
}

/**
 * Create Dictionary Entry DTO
 */
export class CreateDictionaryEntryDto {
  @IsString()
  key: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  enabled?: boolean;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DictionaryValueDto)
  @IsOptional()
  values?: DictionaryValueDto[];
}

/**
 * Update Dictionary Entry DTO
 */
export class UpdateDictionaryEntryDto {
  @IsString()
  @IsOptional()
  key?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  enabled?: boolean;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DictionaryValueDto)
  @IsOptional()
  values?: DictionaryValueDto[];
}

/**
 * Dictionary Entry Query DTO
 */
export class DictionaryQueryDto {
  @IsString()
  @IsOptional()
  key?: string;

  @IsBoolean()
  @IsOptional()
  enabled?: boolean;

  @IsNumber()
  @IsOptional()
  page?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;
}

/**
 * Dictionary Entry Response DTO
 */
export class DictionaryEntryDto {
  id: number;
  key: string;
  description?: string;
  enabled: boolean;
  values: DictionaryValueDto[];
  createdAt: Date;
  updatedAt: Date;
}
