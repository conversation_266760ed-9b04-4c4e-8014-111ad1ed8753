import { IsString, IsOptional, IsN<PERSON>ber, IsBoolean } from 'class-validator';

export class CreateDepartmentDto {
  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsNumber()
  @IsOptional()
  orderNum?: number;

  @IsNumber()
  @IsOptional()
  parentId?: number;

  @IsBoolean()
  @IsOptional()
  status?: boolean;
}

export class UpdateDepartmentDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsNumber()
  @IsOptional()
  orderNum?: number;

  @IsNumber()
  @IsOptional()
  parentId?: number;

  @IsBoolean()
  @IsOptional()
  status?: boolean;
}

export class DepartmentDto {
  id: number;
  name: string;
  description?: string;
  orderNum: number;
  parentId?: number;
  status: boolean;
  createdAt: Date;
  updatedAt: Date;
  children?: DepartmentDto[];
}

export class DepartmentTreeDto {
  id: number;
  name: string;
  parentId?: number;
  children?: DepartmentTreeDto[];
}

export class DepartmentQueryDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;

  @IsNumber()
  @IsOptional()
  parentId?: number;
}
