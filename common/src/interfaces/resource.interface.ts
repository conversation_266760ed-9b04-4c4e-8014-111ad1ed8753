/**
 * Resource type enum
 */
export enum ResourceType {
  MENU = 'menu',
  BUTTON = 'button',
  API = 'api',
  PAGE = 'page'
}

/**
 * Resource interface
 */
export interface IResource {
  id: number;
  code: string;
  name: string;
  description?: string;
  type: ResourceType;
  menuId?: number;
  apiPath?: string;
  apiMethod?: string;
  status: boolean;
  orderNum: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Create Resource DTO
 */
export interface ICreateResourceDto {
  code: string;
  name: string;
  description?: string;
  type: ResourceType;
  menuId?: number;
  apiPath?: string;
  apiMethod?: string;
  status?: boolean;
  orderNum?: number;
}

/**
 * Update Resource DTO
 */
export interface IUpdateResourceDto {
  code?: string;
  name?: string;
  description?: string;
  type?: ResourceType;
  menuId?: number;
  apiPath?: string;
  apiMethod?: string;
  status?: boolean;
  orderNum?: number;
}

/**
 * Resource Query DTO
 */
export interface IResourceQueryDto {
  page?: number;
  limit?: number;
  name?: string;
  code?: string;
  type?: ResourceType;
  menuId?: number;
  status?: boolean;
}
