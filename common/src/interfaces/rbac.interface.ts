/**
 * Role interface
 */
export interface IRole {
  id: number;
  name: string;
  description?: string;
  code: string;
  status: boolean;
  orderNum: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Role Permission interface
 */
export interface IRolePermission {
  id: number;
  roleId: number;
  menuId?: number;
  resourceId: number;
  granted: boolean;
}

/**
 * Create Role DTO
 */
export interface ICreateRoleDto {
  name: string;
  description?: string;
  code: string;
  status?: boolean;
  orderNum?: number;
}

/**
 * Update Role DTO
 */
export interface IUpdateRoleDto {
  name?: string;
  description?: string;
  code?: string;
  status?: boolean;
  orderNum?: number;
}
