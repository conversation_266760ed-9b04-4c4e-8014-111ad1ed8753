import { IResource } from './resource.interface';

/**
 * Menu interface
 */
export interface IMenu {
  id: number;
  name: string;
  path?: string;
  component?: string;
  icon?: string;
  orderNum: number;
  parentId?: number;
  status: boolean;
  hidden: boolean;
  resourceCode?: string;
  children?: IMenu[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Menu with resources interface
 */
export interface IMenuWithResources extends IMenu {
  resources: IResource[];
  children?: IMenuWithResources[];
}

/**
 * Create Menu DTO
 */
export interface ICreateMenuDto {
  name: string;
  path?: string;
  component?: string;
  icon?: string;
  orderNum?: number;
  parentId?: number;
  status?: boolean;
  hidden?: boolean;
  resourceCode?: string;
}

/**
 * Update Menu DTO
 */
export interface IUpdateMenuDto {
  name?: string;
  path?: string;
  component?: string;
  icon?: string;
  orderNum?: number;
  parentId?: number;
  status?: boolean;
  hidden?: boolean;
  resourceCode?: string;
}
