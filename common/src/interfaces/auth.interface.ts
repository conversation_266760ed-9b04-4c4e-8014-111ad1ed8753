import { IUser } from './user.interface';
import { AuthType } from '../enums/auth-type.enum';

/**
 * JWT payload interface
 */
export interface JwtPayload {
  sub: string;
  username: string;
  authType: AuthType;
  roleId?: number;
  roleCode?: string;
  permissions?: string[];
}

/**
 * Authentication response interface
 */
export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: IUser;
  isNewUser: boolean;
}

/**
 * Third-party user information interface
 */
export interface ThirdPartyUserInfo {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
}
