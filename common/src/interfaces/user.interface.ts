import { UserStatus } from '../enums/user-status.enum';
import { LoginType } from '../enums/login-type.enum';
import { IUserAuth } from './user-auth.interface';

/**
 * User interface
 */
export interface IUser {
  id: number;
  username: string;
  email?: string;
  phone?: string;
  token?: string;
  avatar?: string;
  status: UserStatus;
  loginType: LoginType;
  thirdPartyId?: string;
  refreshToken?: string;
  refreshTokenExpiresAt?: Date;
  authMethods?: IUserAuth[];
  createdAt: Date;
  updatedAt: Date;
}
