#!/usr/bin/env ts-node-script

// Register path aliases for TypeORM entities
import { register } from 'tsconfig-paths';
import * as path from "path";

// Register path aliases from tsconfig.json
const baseUrl = path.resolve(__dirname);
register({
  baseUrl,
  paths: {
    "@common/*": ["../common/src/*"],
    "@frontend/*": ["../frontend/*"]
  },
});

import { DataSource } from "typeorm";
import * as fs from "fs";
import * as dotenv from "dotenv";
import { SnakeCaseNamingStrategy } from "./src/database/snake-case-naming.strategy";

// Import entity classes directly
import { User } from "./src/user/domain/user.entity";
import { UserAuth } from "./src/auth/domain/user-auth.entity";
import { Department } from "./src/department/domain/department.entity";
import { Menu } from "./src/menu/domain/menu.entity";
import { DictionaryEntry } from "./src/dictionary/domain/dictionary-entry.entity";
import { DictionaryValue } from "./src/dictionary/domain/dictionary-value.entity";

// 确保从正确的路径加载.env文件
// 如果.env位于backend目录下
const envPath = path.resolve(__dirname, '.env');
dotenv.config({ path: envPath });

// 从环境变量中获取数据库配置
const {
  DB_HOST = '**************',
  DB_PORT = '5432',
  DB_USERNAME = 'demo_shop_user',
  DB_PASSWORD = 'Demo_shop@2024_Secure',
  DB_DATABASE = 'demo_shop',
} = process.env;

// 输出读取到的环境变量(仅用于调试)
console.error("环境变量读取结果:");
console.error(`HOST: ${DB_HOST}`);
console.error(`PORT: ${DB_PORT}`);
console.error(`USERNAME: ${DB_USERNAME}`);
console.error(`PASSWORD: ${DB_PASSWORD}`);
console.error(`DATABASE: ${DB_DATABASE}`);

// 更详细的环境变量调试
console.error("当前目录:", __dirname);
console.error("ENV路径:", envPath);
console.error("ENV文件存在:", fs.existsSync(envPath));
console.error("环境变量:", process.env.DB_DATABASE);
console.error("解构后变量:", DB_DATABASE);

// 在创建连接前再次输出
console.error("创建连接使用的数据库名:", DB_DATABASE);

// 创建临时数据源连接以获取所有实体
async function generateSchema() {
  try {
    // 使用实体路径模式，自动发现所有实体
    const dataSource = new DataSource({
      type: "postgres",
      host: DB_HOST,
      port: parseInt(DB_PORT, 10),
      username: DB_USERNAME,
      password: DB_PASSWORD,
      database: DB_DATABASE,
      entities: [User, UserAuth, Department, Menu],
      synchronize: false,
      namingStrategy: new SnakeCaseNamingStrategy(),
    });

    // 初始化数据源以加载实体
    await dataSource.initialize();

    // 获取所有实体
    const entities = dataSource.entityMetadatas.map(metadata => metadata.target);

    // 使用 Atlas TypeORM Provider
    const { loadEntities } = require("@ariga/atlas-provider-typeorm/build/load");
    const sql = await loadEntities("postgres", entities);

    // 关闭数据源
    await dataSource.destroy();

    // 输出SQL到标准输出（Atlas企业版需要）
    console.log(sql);

    return sql;
  } catch (error) {
    console.error("Error generating schema:", error);
    process.exit(1);
  }
}

// 如果直接运行此脚本，则生成schema.sql文件
if (require.main === module) {
  generateSchema().then(sql => {
    fs.writeFileSync("schema.sql", sql);
    console.error("Schema generated to schema.sql");
  });
} else {
  // 作为模块被导入时，仅导出generateSchema函数
  module.exports = generateSchema;
}