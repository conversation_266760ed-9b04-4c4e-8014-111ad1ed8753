#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to convert camelCase to snake_case
function toSnakeCase(str) {
  return str.replace(/([A-Z])/g, '_$1').toLowerCase();
}

// Function to process SQL and convert column names to snake_case
function convertToSnakeCase(sql) {
  // Step 1: Convert column names in CREATE TABLE statements
  // Regular expression to match column definitions in CREATE TABLE statements
  const columnRegex = /"([a-zA-Z0-9]+)"\s+([^,\n]+)/g;

  // Replace column names with snake_case versions
  let result = sql.replace(columnRegex, (match, columnName, columnDef) => {
    // Skip 'id' column
    if (columnName === 'id') {
      return match;
    }

    // Convert column name to snake_case
    const snakeCaseColumnName = toSnakeCase(columnName);

    // Return the modified column definition
    return `"${snakeCaseColumnName}" ${columnDef}`;
  });

  // Step 2: Convert column names in foreign key constraints
  // Regular expression to match foreign key constraints
  const fkRegex = /FOREIGN KEY \("([a-zA-Z0-9]+)"\) REFERENCES/g;

  // Replace foreign key column names with snake_case versions
  result = result.replace(fkRegex, (match, columnName) => {
    // Convert column name to snake_case
    const snakeCaseColumnName = toSnakeCase(columnName);

    // Return the modified foreign key constraint
    return `FOREIGN KEY ("${snakeCaseColumnName}") REFERENCES`;
  });

  // Step 3: Convert column names in unique constraints
  const uniqueRegex = /CONSTRAINT "([^"]+)" UNIQUE \("([a-zA-Z0-9]+)"\)/g;

  result = result.replace(uniqueRegex, (match, constraintName, columnName) => {
    // Convert column name to snake_case
    const snakeCaseColumnName = toSnakeCase(columnName);

    // Return the modified unique constraint
    return `CONSTRAINT "${constraintName}" UNIQUE ("${snakeCaseColumnName}")`;
  });

  // Step 4: Convert column names in references
  const referencesRegex = /REFERENCES "([^"]+)"\("([a-zA-Z0-9]+)"\)/g;

  result = result.replace(referencesRegex, (match, tableName, columnName) => {
    // Skip 'id' column
    if (columnName === 'id') {
      return match;
    }

    // Convert column name to snake_case
    const snakeCaseColumnName = toSnakeCase(columnName);

    // Return the modified reference
    return `REFERENCES "${tableName}"("${snakeCaseColumnName}")`;
  });

  return result;
}

// Function to fix duplicate enum values
function fixDuplicateEnumValues(sql) {
  // Find all enum definitions
  const enumRegex = /CREATE TYPE "([^"]+)" AS ENUM\(([^\)]+)\);/g;

  return sql.replace(enumRegex, (match, enumName, enumValues) => {
    // Split the enum values by comma
    const values = enumValues.split(',').map(v => v.trim());

    // Remove duplicates
    const uniqueValues = [...new Set(values)];

    // Join the unique values back together
    const uniqueEnumValues = uniqueValues.join(', ');

    // Return the fixed enum definition
    return `CREATE TYPE "${enumName}" AS ENUM(${uniqueEnumValues});`;
  });
}

// Run the TypeORM schema generator
try {
  // Execute the TypeORM schema generator
  const sql = execSync('npx ts-node -r tsconfig-paths/register load.ts', { encoding: 'utf8' });

  // Convert column names to snake_case
  let processedSql = convertToSnakeCase(sql);

  // Fix duplicate enum values
  processedSql = fixDuplicateEnumValues(processedSql);

  // Output the modified SQL
  console.log(processedSql);

  // Also save to schema.sql
  fs.writeFileSync(path.join(__dirname, 'schema.sql'), processedSql);

  console.error('Schema with snake_case column names generated to schema.sql');
} catch (error) {
  console.error('Error generating schema:', error);
  process.exit(1);
}
