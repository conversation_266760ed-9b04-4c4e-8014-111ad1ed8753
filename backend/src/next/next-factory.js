// Enhanced wrapper for Next.js to avoid import issues and handle React 19 compatibility
const next = require('next');
const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

// Set deployment ID for Next.js 15
if (!process.env.NEXT_DEPLOYMENT_ID) {
  process.env.NEXT_DEPLOYMENT_ID = 'development-' + Date.now();
}

// Function to compile Tailwind CSS
function compileTailwindCSS(frontendDir) {
  console.log('Compiling Tailwind CSS before initializing Next.js...');

  try {
    // Check if the compile-tailwind script exists
    const scriptPath = path.join(frontendDir, 'scripts/compile-tailwind.js');

    if (fs.existsSync(scriptPath)) {
      console.log(`Found Tailwind compilation script at: ${scriptPath}`);
      // Execute the script directly
      execSync(`node ${scriptPath}`, { stdio: 'inherit', cwd: frontendDir });
      console.log('Tailwind CSS compilation completed successfully.');
      return true;
    } else {
      console.log('Tailwind compilation script not found, trying npm script...');
      // Try running the npm script
      execSync('npm run compile-tailwind', { stdio: 'inherit', cwd: frontendDir });
      console.log('Tailwind CSS compilation completed successfully via npm script.');
      return true;
    }
  } catch (error) {
    console.error('Error compiling Tailwind CSS:', error.message);
    return false;
  }
}

function createNextApp(options) {
  // Check if SSR is enabled from environment variable
  // Default to true if not specified
  const enableSSR = process.env.ENABLE_SSR !== 'false';

  // Log configuration for debugging
  console.log('Next.js Configuration:');
  console.log(`- Directory: ${options.dir}`);
  console.log(`- Development mode: ${options.dev ? 'enabled' : 'disabled'}`);
  console.log(`- SSR: ${enableSSR ? 'enabled' : 'disabled'}`);

  // Compile Tailwind CSS before initializing Next.js
  compileTailwindCSS(options.dir);

  // Merge the SSR configuration with the provided options
  const nextOptions = {
    ...options,
    // When enableSSR is false, we set Next.js to use static HTML export
    // This effectively disables SSR
    conf: {
      ...options.conf,
      // Control SSR with the environment variable
      // When false, pages will be pre-rendered at build time only
      ssr: enableSSR,
      // Ensure CSS processing is enabled
      optimizeCss: true,
      // Ensure PostCSS processing is enabled
      webpack: (config, { dev, isServer }) => {
        // Apply any custom webpack config from options
        if (options.conf && options.conf.webpack) {
          config = options.conf.webpack(config, { dev, isServer });
        }

        // Make sure Next.js uses the PostCSS config from the frontend
        config.module.rules.forEach((rule) => {
          if (rule.oneOf) {
            rule.oneOf.forEach((oneOfRule) => {
              if (oneOfRule.test && oneOfRule.test.toString().includes('css')) {
                if (oneOfRule.use && Array.isArray(oneOfRule.use)) {
                  oneOfRule.use.forEach((loader) => {
                    if (loader.loader && loader.loader.includes('postcss-loader')) {
                      // Force the PostCSS loader to use the frontend's postcss.config.js
                      loader.options = loader.options || {};
                      loader.options.postcssOptions = loader.options.postcssOptions || {};
                      loader.options.postcssOptions.config = path.join(options.dir, 'postcss.config.js');
                    }
                  });
                }
              }
            });
          }
        });
        return config;
      }
    }
  };

  // Create the Next.js app
  console.log(`Creating Next.js app with SSR ${enableSSR ? 'enabled' : 'disabled'}`);
  const app = next(nextOptions);

  // Return the app
  return app;
}

module.exports = {
  createNextApp
};
