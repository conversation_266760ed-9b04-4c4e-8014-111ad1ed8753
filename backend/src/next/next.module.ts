import { Mo<PERSON><PERSON>, Logger } from '@nestjs/common';
import { NextService } from './next.service';
import { join } from 'path';
import { ConfigService } from '@nestjs/config';
import { existsSync } from 'fs';
import { execSync } from 'child_process';

// Ensure we have a deployment ID for Next.js 15
if (!process.env.NEXT_DEPLOYMENT_ID) {
  process.env.NEXT_DEPLOYMENT_ID = 'development-' + Date.now();
}

@Module({
  providers: [NextService],
  exports: [NextService],
})
export class NextModule {
  private readonly logger = new Logger(NextModule.name);

  constructor(
    private readonly nextService: NextService,
  ) {}

  /**
   * Ensure the public/css directory exists in the frontend
   */
  private ensureCssDirectory(frontendDir: string): void {
    const fs = require('fs');
    const cssDir = join(frontendDir, 'public/css');

    if (!fs.existsSync(cssDir)) {
      this.logger.log(`Creating CSS output directory: ${cssDir}`);
      fs.mkdirSync(cssDir, { recursive: true });
    }
  }

  /**
   * Prepare the Next.js application
   */
  public async prepare() {
    const dir = join(process.cwd(), '..', 'frontend');
    const enableSSR = process.env.ENABLE_SSR !== 'false';

    this.logger.log(`Initializing Next.js with SSR ${enableSSR ? 'enabled' : 'disabled'}`);
    this.logger.log(`Frontend directory: ${dir}`);

    // Verify the frontend directory exists
    if (!existsSync(dir)) {
      this.logger.error(`Frontend directory not found at: ${dir}`);
      throw new Error(`Frontend directory not found at: ${dir}`);
    }

    // Ensure the CSS output directory exists
    this.ensureCssDirectory(dir);

    // Verify the postcss.config.js file exists
    const postcssConfigPath = join(dir, 'postcss.config.js');
    if (!existsSync(postcssConfigPath)) {
      this.logger.error(`PostCSS config file not found at: ${postcssConfigPath}`);
    } else {
      this.logger.log(`PostCSS config file found at: ${postcssConfigPath}`);
    }

    // Verify the tailwind.config.js file exists
    const tailwindConfigPath = join(dir, 'tailwind.config.js');
    if (!existsSync(tailwindConfigPath)) {
      this.logger.error(`Tailwind config file not found at: ${tailwindConfigPath}`);
    } else {
      this.logger.log(`Tailwind config file found at: ${tailwindConfigPath}`);
    }

    // Verify the compile-tailwind.js script exists
    const tailwindScriptPath = join(dir, 'scripts/compile-tailwind.js');
    if (!existsSync(tailwindScriptPath)) {
      this.logger.warn(`Tailwind compilation script not found at: ${tailwindScriptPath}`);
      this.logger.warn('CSS may not be properly processed during server startup.');
    } else {
      this.logger.log(`Tailwind compilation script found at: ${tailwindScriptPath}`);
    }

    return this.nextService.prepare({
      dev: process.env.NODE_ENV !== 'production',
      dir,
      conf: {
        // Pass the SSR configuration
        ssr: enableSSR,
        // Ensure CSS processing is enabled
        optimizeCss: true,
        // Ensure PostCSS is used
        postcssOptions: {
          config: postcssConfigPath
        }
      }
    });
  }
}
