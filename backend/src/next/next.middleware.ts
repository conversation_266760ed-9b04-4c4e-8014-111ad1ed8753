import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { NextService } from './next.service';
import { parse } from 'url';

@Injectable()
export class NextMiddleware implements NestMiddleware {
  private readonly logger = new Logger(NextMiddleware.name);
  private readonly enableSSR: boolean;

  constructor(private readonly nextService: NextService) {
    // Read the SSR configuration from environment variables
    // Default to true if not specified
    this.enableSSR = process.env.ENABLE_SSR !== 'false';
    this.logger.log(`Next.js middleware initialized with SSR ${this.enableSSR ? 'enabled' : 'disabled'}`);
  }

  public async use(req: Request, res: Response, next: NextFunction) {
    try {
      // If the request is for a NestJS API route, let NestJS handle it
      if (req.originalUrl.startsWith('/api')) {
        this.logger.debug(`Skipping Next.js middleware for API route: ${req.originalUrl}`);
        // Important: Just call next() and return immediately to ensure NestJS handles the API route
        next();
        return;
      }

      // For debugging
      this.logger.debug(`Handling request with Next.js: ${req.originalUrl} (SSR: ${this.enableSSR})`);

      // Otherwise, let Next.js handle it
      const app = this.nextService.getApp();

      // Special handling for root route
      if (req.originalUrl === '/') {
        this.logger.debug('Handling root route with Next.js');
        try {
          return app.render(req, res, '/');
        } catch (renderError) {
          this.logger.error(`Error rendering root route: ${renderError.message}`);
          res.statusCode = 500;
          res.end('Internal Server Error');
          return;
        }
      }

      const parsedUrl = parse(req.originalUrl, true);
      // this.logger.debug(`[NextMiddleware] Parsed URL: ${JSON.stringify(parsedUrl)}`);

      try {
        // The Next.js request handler will automatically respect the SSR configuration
        // that was set during initialization
        return app.getRequestHandler()(req, res, parsedUrl);
      } catch (handlerError) {
        this.logger.error(`Error in Next.js request handler: ${handlerError.message}`);
        res.statusCode = 500;
        res.end('Internal Server Error');
        return;
      }
    } catch (error) {
      this.logger.error(`Error in NextMiddleware: ${error.message}`);
      res.statusCode = 500;
      res.end('Internal Server Error');
      return;
    }
  }
}
