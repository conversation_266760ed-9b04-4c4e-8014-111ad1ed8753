import { Injectable, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { join } from 'path';
// Import the factory function from our custom wrapper
const { createNextApp } = require('./next-factory');

@Injectable()
export class NextService {
  private app;
  private readonly logger = new Logger(NextService.name);
  private enableSSR: boolean;

  constructor() {
    // Read the SSR configuration from environment variables
    // Default to true if not specified
    this.enableSSR = process.env.ENABLE_SSR !== 'false';
  }

  public getApp() {
    if (!this.app) {
      this.logger.error('Attempting to access Next.js app before initialization');
      throw new Error('Next.js server is not initialized');
    }
    return this.app;
  }

  public async prepare(options: any) {
    try {
      this.logger.log(`Next.js app directory: ${options.dir}`);
      this.logger.log(`SSR is ${this.enableSSR ? 'enabled' : 'disabled'}`);

      // Create a Next.js app with the provided options
      this.app = createNextApp(options);

      // Prepare the Next.js app
      await this.app.prepare();
      this.logger.log('Next.js app is ready');
      return this.app;
    } catch (error) {
      this.logger.error(`Error initializing Next.js: ${error.message}`);
      throw error;
    }
  }

  public getRequestHandler() {
    if (!this.app) {
      this.logger.warn('Attempting to use Next.js request handler before initialization');
      return (req, res) => {
        res.statusCode = 500;
        res.end('Next.js server is not initialized');
      };
    }
    return this.app.getRequestHandler();
  }

  public render(
    req: Request,
    res: Response,
    pathname: string,
    query?: any,
  ): Promise<void> {
    if (!this.app) {
      this.logger.error('Attempting to render Next.js page before initialization');
      res.statusCode = 500;
      res.end('Next.js server is not initialized');
      return Promise.resolve();
    }
    this.logger.log(`[NextService] Rendering page: ${pathname} with query: ${JSON.stringify(query)} (SSR: ${this.enableSSR})`);
    return this.app.render(req, res, pathname, query);
  }

  public renderError(
    req: Request,
    res: Response,
    err: Error,
    pathname: string,
    query?: any,
  ): Promise<void> {
    if (!this.app) {
      this.logger.error('Attempting to render Next.js error page before initialization');
      res.statusCode = 500;
      res.end('Next.js server is not initialized');
      return Promise.resolve();
    }
    return this.app.renderError(err, req, res, pathname, query);
  }
}
