import { Controller, Get, Post, Put, Delete, Body, Param, Query, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DictionaryService } from './dictionary.service';
import { CreateDictionaryEntryDto, UpdateDictionaryEntryDto, DictionaryQueryDto } from '@common/dto/dictionary.dto';

@ApiTags('dictionary')
@Controller('dictionary')
export class DictionaryController {
  constructor(private readonly dictionaryService: DictionaryService) {}

  @Get()
  @ApiOperation({ summary: 'Get all dictionary entries with optional filtering' })
  @ApiResponse({ status: 200, description: 'Return all dictionary entries' })
  async findAll(@Query() query: DictionaryQueryDto) {
    const [entries, total] = await this.dictionaryService.findAll(query);
    return {
      data: entries,
      total
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get dictionary entry by id' })
  @ApiResponse({ status: 200, description: 'Return dictionary entry by id' })
  @ApiResponse({ status: 404, description: 'Dictionary entry not found' })
  async findOne(@Param('id') id: number) {
    return this.dictionaryService.findOne(id);
  }

  @Get('key/:key')
  @ApiOperation({ summary: 'Get dictionary entry by key' })
  @ApiResponse({ status: 200, description: 'Return dictionary entry by key' })
  @ApiResponse({ status: 404, description: 'Dictionary entry not found' })
  async findByKey(@Param('key') key: string) {
    return this.dictionaryService.findByKey(key);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new dictionary entry' })
  @ApiResponse({ status: 201, description: 'The dictionary entry has been successfully created' })
  async create(@Body() createDictionaryEntryDto: CreateDictionaryEntryDto) {
    return this.dictionaryService.create(createDictionaryEntryDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a dictionary entry' })
  @ApiResponse({ status: 200, description: 'The dictionary entry has been successfully updated' })
  @ApiResponse({ status: 404, description: 'Dictionary entry not found' })
  async update(@Param('id') id: number, @Body() updateDictionaryEntryDto: UpdateDictionaryEntryDto) {
    return this.dictionaryService.update(id, updateDictionaryEntryDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a dictionary entry' })
  @ApiResponse({ status: 200, description: 'The dictionary entry has been successfully deleted' })
  @ApiResponse({ status: 404, description: 'Dictionary entry not found' })
  async remove(@Param('id') id: number) {
    await this.dictionaryService.remove(id);
    return { success: true };
  }

  @Post(':id/values')
  @ApiOperation({ summary: 'Add a value to a dictionary entry' })
  @ApiResponse({ status: 201, description: 'The value has been successfully added' })
  @ApiResponse({ status: 404, description: 'Dictionary entry not found' })
  async addValue(
    @Param('id') id: number,
    @Body() body: { value: string; label?: string }
  ) {
    return this.dictionaryService.addValue(id, body.value, body.label);
  }

  @Delete('values/:id')
  @ApiOperation({ summary: 'Remove a value from a dictionary entry' })
  @ApiResponse({ status: 200, description: 'The value has been successfully removed' })
  @ApiResponse({ status: 404, description: 'Dictionary value not found' })
  async removeValue(@Param('id') id: number) {
    await this.dictionaryService.removeValue(id);
    return { success: true };
  }
}
