import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DictionaryEntry } from './domain/dictionary-entry.entity';
import { DictionaryValue } from './domain/dictionary-value.entity';
import { CreateDictionaryEntryDto, UpdateDictionaryEntryDto, DictionaryQueryDto } from '@common/dto/dictionary.dto';

@Injectable()
export class DictionaryService {
  constructor(
    @InjectRepository(DictionaryEntry)
    private readonly dictionaryEntryRepository: Repository<DictionaryEntry>,
    @InjectRepository(DictionaryValue)
    private readonly dictionaryValueRepository: Repository<DictionaryValue>,
  ) {}

  /**
   * Find all dictionary entries with optional filtering
   */
  async findAll(query: DictionaryQueryDto): Promise<[DictionaryEntry[], number]> {
    const { key, enabled, page = 1, limit = 10 } = query;
    
    const queryBuilder = this.dictionaryEntryRepository.createQueryBuilder('entry')
      .leftJoinAndSelect('entry.values', 'values')
      .orderBy('entry.key', 'ASC')
      .addOrderBy('values.orderNum', 'ASC');
    
    if (key) {
      queryBuilder.andWhere('entry.key LIKE :key', { key: `%${key}%` });
    }
    
    if (enabled !== undefined) {
      queryBuilder.andWhere('entry.enabled = :enabled', { enabled });
    }
    
    return queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();
  }

  /**
   * Find a dictionary entry by ID
   */
  async findOne(id: number): Promise<DictionaryEntry> {
    const entry = await this.dictionaryEntryRepository.findOne({
      where: { id },
      relations: ['values'],
    });
    
    if (!entry) {
      throw new NotFoundException(`Dictionary entry with ID ${id} not found`);
    }
    
    return entry;
  }

  /**
   * Find a dictionary entry by key
   */
  async findByKey(key: string): Promise<DictionaryEntry> {
    const entry = await this.dictionaryEntryRepository.findOne({
      where: { key },
      relations: ['values'],
    });
    
    if (!entry) {
      throw new NotFoundException(`Dictionary entry with key ${key} not found`);
    }
    
    return entry;
  }

  /**
   * Create a new dictionary entry
   */
  async create(createDto: CreateDictionaryEntryDto): Promise<DictionaryEntry> {
    const entry = this.dictionaryEntryRepository.create({
      key: createDto.key,
      description: createDto.description,
      enabled: createDto.enabled ?? true,
    });
    
    await this.dictionaryEntryRepository.save(entry);
    
    if (createDto.values && createDto.values.length > 0) {
      const values = createDto.values.map((valueDto, index) => 
        this.dictionaryValueRepository.create({
          value: valueDto.value,
          label: valueDto.label,
          orderNum: valueDto.orderNum ?? index,
          entry,
        })
      );
      
      entry.values = await this.dictionaryValueRepository.save(values);
    }
    
    return entry;
  }

  /**
   * Update a dictionary entry
   */
  async update(id: number, updateDto: UpdateDictionaryEntryDto): Promise<DictionaryEntry> {
    const entry = await this.findOne(id);
    
    if (updateDto.key !== undefined) {
      entry.key = updateDto.key;
    }
    
    if (updateDto.description !== undefined) {
      entry.description = updateDto.description;
    }
    
    if (updateDto.enabled !== undefined) {
      entry.enabled = updateDto.enabled;
    }
    
    await this.dictionaryEntryRepository.save(entry);
    
    if (updateDto.values !== undefined) {
      // Delete existing values
      await this.dictionaryValueRepository.delete({ entryId: id });
      
      if (updateDto.values.length > 0) {
        // Create new values
        const values = updateDto.values.map((valueDto, index) => 
          this.dictionaryValueRepository.create({
            value: valueDto.value,
            label: valueDto.label,
            orderNum: valueDto.orderNum ?? index,
            entry,
          })
        );
        
        entry.values = await this.dictionaryValueRepository.save(values);
      } else {
        entry.values = [];
      }
    }
    
    return entry;
  }

  /**
   * Delete a dictionary entry
   */
  async remove(id: number): Promise<void> {
    const entry = await this.findOne(id);
    await this.dictionaryEntryRepository.remove(entry);
  }

  /**
   * Add a value to a dictionary entry
   */
  async addValue(entryId: number, value: string, label?: string): Promise<DictionaryValue> {
    const entry = await this.findOne(entryId);
    
    // Get the highest order number
    const maxOrderNum = entry.values.reduce(
      (max, val) => (val.orderNum > max ? val.orderNum : max),
      -1
    );
    
    const dictionaryValue = this.dictionaryValueRepository.create({
      value,
      label,
      orderNum: maxOrderNum + 1,
      entry,
    });
    
    return this.dictionaryValueRepository.save(dictionaryValue);
  }

  /**
   * Remove a value from a dictionary entry
   */
  async removeValue(valueId: number): Promise<void> {
    const value = await this.dictionaryValueRepository.findOne({
      where: { id: valueId },
    });
    
    if (!value) {
      throw new NotFoundException(`Dictionary value with ID ${valueId} not found`);
    }
    
    await this.dictionaryValueRepository.remove(value);
  }
}
