import { <PERSON><PERSON>ty, <PERSON>umn, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { DictionaryEntry } from './dictionary-entry.entity';

@Entity('dictionary_values')
export class DictionaryValue {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  value: string;

  @Column({ nullable: true })
  label: string;

  @Column({ default: 0 })
  orderNum: number;

  @ManyToOne(() => DictionaryEntry, entry => entry.values, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'entry_id' })
  entry: DictionaryEntry;

  @Column()
  entryId: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
