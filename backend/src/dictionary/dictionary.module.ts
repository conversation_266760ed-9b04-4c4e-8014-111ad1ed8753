import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DictionaryController } from './dictionary.controller';
import { DictionaryService } from './dictionary.service';
import { DictionaryEntry } from './domain/dictionary-entry.entity';
import { DictionaryValue } from './domain/dictionary-value.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([DictionaryEntry, DictionaryValue]),
  ],
  controllers: [DictionaryController],
  providers: [DictionaryService],
  exports: [DictionaryService],
})
export class DictionaryModule {}
