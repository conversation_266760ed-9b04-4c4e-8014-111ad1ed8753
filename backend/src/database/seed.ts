import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { User } from '../user/domain/user.entity';
import { UserAuth } from '../auth/domain/user-auth.entity';
import { Menu } from '../menu/domain/menu.entity';
import { Role } from '../role/domain/role.entity';
import { Resource } from '../resource/domain/resource.entity';
import { RoleMenuResource } from '../rbac/domain/role-menu-resource.entity';
import { SnakeCaseNamingStrategy } from './snake-case-naming.strategy';
import { seedMenuData } from './seeds/menu-seed';
import { seedRbacData } from './seeds/rbac-seed';
import { seedAdminUser } from './seeds/admin-user-seed';

// Load environment variables
config();

const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || '**************',
  port: parseInt(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'demo_shop_user',
  password: process.env.DB_PASSWORD || 'Demo_shop@2024_Secure',
  database: process.env.DB_DATABASE || 'demo_shop',
  entities: [User, UserAuth, Menu, Role, Resource, RoleMenuResource],
  namingStrategy: new SnakeCaseNamingStrategy(),
});

async function runSeeds() {
  try {
    console.log('Initializing database connection...');
    await dataSource.initialize();
    console.log('Database connection established.');

    // Run menu seed first (creates menu structure)
    await seedMenuData(dataSource);

    // Run RBAC seed (creates roles, resources, and permissions)
    await seedRbacData(dataSource);

    // Run admin user seed (requires RBAC data to exist)
    await seedAdminUser(dataSource);

    console.log('All seeds completed successfully!');
    console.log('\n=== IMPORTANT ===');
    console.log('Default admin user has been created:');
    console.log('  Username: admin');
    console.log('  Password: Qweqwe');
    console.log('  Phone: 13800138000 (for SMS verification)');
    console.log('  Role: Super Admin (full permissions)');
    console.log('================\n');
  } catch (error) {
    console.error('Error running seeds:', error);
  } finally {
    await dataSource.destroy();
    console.log('Database connection closed.');
  }
}

runSeeds();
