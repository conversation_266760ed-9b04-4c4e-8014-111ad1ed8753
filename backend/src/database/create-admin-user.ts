import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { User } from '../user/domain/user.entity';
import { UserAuth } from '../auth/domain/user-auth.entity';
import { Menu } from '../menu/domain/menu.entity';
import { Role } from '../role/domain/role.entity';
import { Resource } from '../resource/domain/resource.entity';
import { RoleMenuResource } from '../rbac/domain/role-menu-resource.entity';
import { SnakeCaseNamingStrategy } from './snake-case-naming.strategy';
import { seedMenuData } from './seeds/menu-seed';
import { seedRbacData } from './seeds/rbac-seed';
import { seedAdminUser } from './seeds/admin-user-seed';

// Load environment variables
config();

const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || '**************',
  port: parseInt(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'demo_shop_user',
  password: process.env.DB_PASSWORD || 'Demo_shop@2024_Secure',
  database: process.env.DB_DATABASE || 'demo_shop',
  entities: [User, UserAuth, Menu, Role, Resource, RoleMenuResource],
  namingStrategy: new SnakeCaseNamingStrategy(),
});

/**
 * Standalone script to create admin user
 * This can be run independently if you only need to create the admin user
 */
async function createAdminUser() {
  try {
    console.log('Initializing database connection...');
    await dataSource.initialize();
    console.log('Database connection established.');

    // Ensure menu data exists
    await seedMenuData(dataSource);

    // Ensure RBAC data exists
    await seedRbacData(dataSource);

    // Create admin user
    await seedAdminUser(dataSource);

    console.log('\n=== ADMIN USER CREATED SUCCESSFULLY ===');
    console.log('You can now login with:');
    console.log('  Username: admin');
    console.log('  Password: Qweqwe');
    console.log('  Phone: 13800138000 (for SMS verification)');
    console.log('  Auth Type: SMS_VERIFICATION or PASSWORD');
    console.log('========================================\n');
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  } finally {
    await dataSource.destroy();
    console.log('Database connection closed.');
  }
}

createAdminUser();
