# Default Admin User Setup

This document describes the default admin user account that can be created for the system.

## Admin User Specifications

- **Username**: `admin`
- **Password**: `Qweqwe`
- **Phone**: `***********` (for SMS verification)
- **Email**: `<EMAIL>`
- **Role**: Super Admin (SUPER_ADMIN)
- **Permissions**: Full administrative access to all system features
- **Status**: Active

## Authentication Methods

The admin user supports both authentication methods:

1. **Password Authentication**
   - Auth Type: `PASSWORD`
   - Account: `admin`
   - Credential: Hashed password (`Qweqwe`)

2. **SMS Verification**
   - Auth Type: `SMS_VERIFICATION`
   - Account: `***********`
   - Credential: Dynamic SMS verification code

## Creating the Admin User

### Option 1: Run Full Database Seeding (Recommended)

This will create all menu data, RBAC data (roles, permissions, resources), and the admin user:

```bash
cd backend
npm run seed
```

This command will:
1. Create the menu structure (Dashboard, System Management, etc.)
2. Create roles (SUPER_ADMIN, ADMIN, USER)
3. Create resources and link them to menus
4. Assign all permissions to SUPER_ADMIN role
5. Create the admin user with SUPER_ADMIN role

### Option 2: Create Admin User Only

If you already have menu and RBAC data and only need to create the admin user:

```bash
cd backend
npm run seed:admin
```

**Note**: This will also ensure menu and RBAC data exists before creating the admin user.

### Option 3: Manual Creation via TypeScript

You can also import and use the seeding function directly:

```typescript
import { seedAdminUser } from './src/database/seeds/admin-user-seed';
import { dataSource } from './src/database/data-source';

await dataSource.initialize();
await seedAdminUser(dataSource);
await dataSource.destroy();
```

## Login Examples

### Password Login

```typescript
const loginResponse = await authApi.login({
  account: 'admin',
  authCredential: 'Qweqwe',
  authType: 'PASSWORD',
  rememberMe: false
});
```

### SMS Verification Login

1. First, send SMS code:
```typescript
await smsApi.sendSmsCode({
  phone: '***********',
  captchaToken: 'your-captcha-token',
  captchaText: 'your-captcha-text'
});
```

2. Then login with the received code:
```typescript
const loginResponse = await authApi.login({
  account: '***********',
  authCredential: 'received-sms-code',
  authType: 'SMS_VERIFICATION',
  rememberMe: false
});
```

## Security Considerations

1. **Change Default Password**: After first login, change the default password to something more secure.

2. **Update Phone Number**: Consider updating the phone number to a real number for SMS verification.

3. **Monitor Admin Access**: Keep track of admin user login activities.

4. **Role-Based Access**: The admin user has SUPER_ADMIN role with full permissions. Consider creating additional admin roles with limited permissions for day-to-day operations.

## Troubleshooting

### Admin User Already Exists

If you run the seeding script and the admin user already exists, the script will skip creation and display a message. This is normal behavior to prevent duplicate admin users.

### RBAC Data Missing

If you run `npm run seed:admin` and get an error about missing SUPER_ADMIN role or menus, the script will automatically create the required data. However, if you prefer to run the full seeding:

```bash
npm run seed
```

### Database Connection Issues

Ensure your database connection settings in `.env` or the seeding scripts are correct:

- Host: `**************`
- Port: `5432`
- Username: `demo_shop_user`
- Password: `Demo_shop@2024_Secure`
- Database: `demo_shop`

## File Structure

```
backend/src/database/
├── seeds/
│   ├── menu-seed.ts           # Menu structure seeding
│   ├── rbac-seed.ts           # RBAC data seeding (roles, resources, permissions)
│   └── admin-user-seed.ts     # Admin user seeding logic
├── seed.ts                    # Main seeding script (runs all seeds)
├── create-admin-user.ts       # Standalone admin user creation
├── test-admin-menu.ts         # Test script for admin menu access
└── README-admin-user.md       # This documentation
```
