import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { User } from '../user/domain/user.entity';
import { UserAuth } from '../auth/domain/user-auth.entity';
import { Menu } from '../menu/domain/menu.entity';
import { Role } from '../role/domain/role.entity';
import { Resource } from '../resource/domain/resource.entity';
import { RoleMenuResource } from '../rbac/domain/role-menu-resource.entity';
import { DictionaryEntry } from '../dictionary/domain/dictionary-entry.entity';
import { DictionaryValue } from '../dictionary/domain/dictionary-value.entity';
import { SnakeCaseNamingStrategy } from './snake-case-naming.strategy';

config();

export default new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  entities: [User, UserAuth, Menu, Role, Resource, RoleMenuResource, DictionaryEntry, DictionaryValue],
  migrations: ['src/database/migrations/*{.ts,.js}'],
  namingStrategy: new SnakeCaseNamingStrategy(),
});