import { DefaultNamingStrategy, NamingStrategyInterface } from 'typeorm';
import { snakeCase } from 'typeorm/util/StringUtils';

/**
 * Snake case naming strategy for TypeORM
 * Automatically converts camelCase property names to snake_case column names
 */
export class SnakeCaseNamingStrategy extends DefaultNamingStrategy implements NamingStrategyInterface {
  /**
   * Converts table name to snake case
   */
  tableName(targetName: string, userSpecifiedName: string | undefined): string {
    return userSpecifiedName || snakeCase(targetName);
  }

  /**
   * Converts column name to snake case
   */
  columnName(
    propertyName: string,
    customName: string | undefined,
    embeddedPrefixes: string[],
  ): string {
    // If a custom name is provided, use it
    if (customName) return customName;

    // Otherwise convert to snake_case
    return snakeCase(
      embeddedPrefixes.concat(propertyName).join('_'),
    );
  }

  /**
   * Converts relation name to snake case
   */
  relationName(propertyName: string): string {
    return snakeCase(propertyName);
  }

  /**
   * Converts join column name to snake case
   */
  joinColumnName(relationName: string, referencedColumnName: string): string {
    return snakeCase(relationName + '_' + referencedColumnName);
  }

  /**
   * Converts join table name to snake case
   */
  joinTableName(
    firstTableName: string,
    secondTableName: string,
    firstPropertyName: string,
    secondPropertyName: string,
  ): string {
    return snakeCase(
      firstTableName +
        '_' +
        firstPropertyName.replace(/\./gi, '_') +
        '_' +
        secondTableName,
    );
  }

  /**
   * Converts join table column name to snake case
   */
  joinTableColumnName(
    tableName: string,
    propertyName: string,
    columnName?: string,
  ): string {
    return snakeCase(
      tableName + '_' + (columnName || propertyName),
    );
  }

  /**
   * Converts index name to snake case
   */
  indexName(tableOrName: string, columns: string[]): string {
    const clonedColumns = [...columns];
    clonedColumns.sort();
    return 'idx_' + snakeCase(tableOrName + '_' + clonedColumns.join('_'));
  }

  /**
   * Converts primary key name to snake case
   */
  primaryKeyName(tableOrName: string, columnNames: string[]): string {
    const clonedColumnNames = [...columnNames];
    clonedColumnNames.sort();
    return 'pk_' + snakeCase(tableOrName + '_' + clonedColumnNames.join('_'));
  }

  /**
   * Converts unique constraint name to snake case
   */
  uniqueConstraintName(tableOrName: string, columnNames: string[]): string {
    const clonedColumnNames = [...columnNames];
    clonedColumnNames.sort();
    return 'uq_' + snakeCase(tableOrName + '_' + clonedColumnNames.join('_'));
  }

  /**
   * Converts foreign key name to snake case
   */
  foreignKeyName(
    tableOrName: string,
    columnNames: string[],
    referencedTablePath?: string,
    referencedColumnNames?: string[],
  ): string {
    const clonedColumnNames = [...columnNames];
    clonedColumnNames.sort();
    
    let name = 'fk_' + snakeCase(tableOrName + '_' + clonedColumnNames.join('_'));
    
    if (referencedTablePath && referencedColumnNames) {
      const clonedReferencedColumnNames = [...referencedColumnNames];
      clonedReferencedColumnNames.sort();
      name += '_' + snakeCase(referencedTablePath + '_' + clonedReferencedColumnNames.join('_'));
    }
    
    return name;
  }
}
