import { DataSource, Not } from 'typeorm';
import { Role } from '../../role/domain/role.entity';
import { Resource, ResourceType } from '../../resource/domain/resource.entity';
import { Menu } from '../../menu/domain/menu.entity';
import { RoleMenuResource } from '../../rbac/domain/role-menu-resource.entity';

export async function seedRbacData(dataSource: DataSource) {
  const roleRepository = dataSource.getRepository(Role);
  const resourceRepository = dataSource.getRepository(Resource);
  const menuRepository = dataSource.getRepository(Menu);
  const roleMenuResourceRepository = dataSource.getRepository(RoleMenuResource);

  console.log('Seeding RBAC data...');

  // Create default roles
  const roles = [
    {
      name: 'Super Admin',
      description: 'System super administrator with all permissions',
      code: 'SUPER_ADMIN',
      status: true,
      orderNum: 1,
    },
    {
      name: 'Admin',
      description: 'System administrator',
      code: 'ADMIN',
      status: true,
      orderNum: 2,
    },
    {
      name: 'User',
      description: 'Regular user',
      code: 'USER',
      status: true,
      orderNum: 3,
    },
  ];

  const savedRoles = [];
  for (const roleData of roles) {
    let role = await roleRepository.findOne({ where: { code: roleData.code } });
    if (!role) {
      role = roleRepository.create(roleData);
      role = await roleRepository.save(role);
      console.log(`Created role: ${role.name}`);
    }
    savedRoles.push(role);
  }

  // Create default resources
  const resources = [
    // Dashboard
    { code: 'DASHBOARD', name: 'Dashboard Access', description: 'Access to dashboard', type: ResourceType.MENU },

    // User Management
    { code: 'USER_MANAGEMENT', name: 'User Management', description: 'Access to user management', type: ResourceType.MENU },
    { code: 'USER_LIST', name: 'User List', description: 'View user list', type: ResourceType.BUTTON },
    { code: 'USER_VIEW', name: 'View User', description: 'View user details', type: ResourceType.BUTTON },
    { code: 'USER_CREATE', name: 'Create User', description: 'Create new user', type: ResourceType.BUTTON },
    { code: 'USER_UPDATE', name: 'Update User', description: 'Update user information', type: ResourceType.BUTTON },
    { code: 'USER_DELETE', name: 'Delete User', description: 'Delete user', type: ResourceType.BUTTON },
    { code: 'USER_STATUS_TOGGLE', name: 'Toggle User Status', description: 'Enable/disable user status', type: ResourceType.BUTTON },

    // Role Management
    { code: 'ROLE_MANAGEMENT', name: 'Role Management', description: 'Access to role management', type: ResourceType.MENU },
    { code: 'ROLE_LIST', name: 'Role List', description: 'View role list', type: ResourceType.BUTTON },
    { code: 'ROLE_VIEW', name: 'View Role', description: 'View role details', type: ResourceType.BUTTON },
    { code: 'ROLE_CREATE', name: 'Create Role', description: 'Create new role', type: ResourceType.BUTTON },
    { code: 'ROLE_UPDATE', name: 'Update Role', description: 'Update role information', type: ResourceType.BUTTON },
    { code: 'ROLE_DELETE', name: 'Delete Role', description: 'Delete role', type: ResourceType.BUTTON },

    // Permission Management
    { code: 'PERMISSION_MANAGEMENT', name: 'Permission Management', description: 'Access to permission management', type: ResourceType.MENU },
    { code: 'PERMISSION_ASSIGN', name: 'Assign Permission', description: 'Assign permissions to roles', type: ResourceType.BUTTON },
    { code: 'PERMISSION_VIEW', name: 'View Permission', description: 'View permissions', type: ResourceType.BUTTON },

    // Menu Management
    { code: 'MENU_MANAGEMENT', name: 'Menu Management', description: 'Access to menu management', type: ResourceType.MENU },
    { code: 'MENU_LIST', name: 'Menu List', description: 'View menu list', type: ResourceType.BUTTON },
    { code: 'MENU_VIEW', name: 'View Menu', description: 'View menu details', type: ResourceType.BUTTON },
    { code: 'MENU_CREATE', name: 'Create Menu', description: 'Create new menu', type: ResourceType.BUTTON },
    { code: 'MENU_UPDATE', name: 'Update Menu', description: 'Update menu information', type: ResourceType.BUTTON },
    { code: 'MENU_DELETE', name: 'Delete Menu', description: 'Delete menu', type: ResourceType.BUTTON },

    // Resource Management
    { code: 'RESOURCE_MANAGEMENT', name: 'Resource Management', description: 'Access to resource management', type: ResourceType.MENU },
    { code: 'RESOURCE_LIST', name: 'Resource List', description: 'View resource list', type: ResourceType.BUTTON },
    { code: 'RESOURCE_VIEW', name: 'View Resource', description: 'View resource details', type: ResourceType.BUTTON },
    { code: 'RESOURCE_CREATE', name: 'Create Resource', description: 'Create new resource', type: ResourceType.BUTTON },
    { code: 'RESOURCE_UPDATE', name: 'Update Resource', description: 'Update resource information', type: ResourceType.BUTTON },
    { code: 'RESOURCE_DELETE', name: 'Delete Resource', description: 'Delete resource', type: ResourceType.BUTTON },

    // Department Management
    { code: 'DEPARTMENT_MANAGEMENT', name: 'Department Management', description: 'Access to department management', type: ResourceType.MENU },
    { code: 'DEPARTMENT_LIST', name: 'Department List', description: 'View department list', type: ResourceType.BUTTON },
    { code: 'DEPARTMENT_VIEW', name: 'View Department', description: 'View department details', type: ResourceType.BUTTON },
    { code: 'DEPARTMENT_CREATE', name: 'Create Department', description: 'Create new department', type: ResourceType.BUTTON },
    { code: 'DEPARTMENT_UPDATE', name: 'Update Department', description: 'Update department information', type: ResourceType.BUTTON },
    { code: 'DEPARTMENT_DELETE', name: 'Delete Department', description: 'Delete department', type: ResourceType.BUTTON },

    // Dictionary Management
    { code: 'DICTIONARY_MANAGEMENT', name: 'Dictionary Management', description: 'Access to dictionary management', type: ResourceType.MENU },
    { code: 'DICTIONARY_LIST', name: 'Dictionary List', description: 'View dictionary list', type: ResourceType.BUTTON },
    { code: 'DICTIONARY_VIEW', name: 'View Dictionary', description: 'View dictionary details', type: ResourceType.BUTTON },
    { code: 'DICTIONARY_CREATE', name: 'Create Dictionary', description: 'Create new dictionary', type: ResourceType.BUTTON },
    { code: 'DICTIONARY_UPDATE', name: 'Update Dictionary', description: 'Update dictionary information', type: ResourceType.BUTTON },
    { code: 'DICTIONARY_DELETE', name: 'Delete Dictionary', description: 'Delete dictionary', type: ResourceType.BUTTON },

    // System Monitoring
    { code: 'ONLINE_USERS', name: 'Online Users', description: 'View online users', type: ResourceType.MENU },
    { code: 'SERVER_MONITOR', name: 'Server Monitor', description: 'Monitor server status', type: ResourceType.MENU },
    { code: 'OPERATION_LOGS', name: 'Operation Logs', description: 'View operation logs', type: ResourceType.MENU },

    // System Tools
    { code: 'CODE_GENERATOR', name: 'Code Generator', description: 'Generate code', type: ResourceType.MENU },
    { code: 'SWAGGER_UI', name: 'Swagger UI', description: 'API documentation', type: ResourceType.MENU },
    { code: 'DATA_BACKUP', name: 'Data Backup', description: 'Backup system data', type: ResourceType.MENU },
  ];

  const savedResources = [];
  for (const [index, resourceData] of resources.entries()) {
    let resource = await resourceRepository.findOne({ where: { code: resourceData.code } });
    if (!resource) {
      resource = resourceRepository.create({
        ...resourceData,
        status: true,
        orderNum: index + 1,
      });
      resource = await resourceRepository.save(resource);
      console.log(`Created resource: ${resource.name}`);
    }
    savedResources.push(resource);
  }

  // Link resources to menus by finding menus with matching resource codes
  const menusWithResources = await menuRepository.find({
    where: { resourceCode: Not(null) },
  });

  for (const menu of menusWithResources) {
    if (menu.resourceCode) {
      const resource = savedResources.find(r => r.code === menu.resourceCode);
      if (resource && !resource.menuId) {
        resource.menuId = menu.id;
        await resourceRepository.save(resource);
        console.log(`Linked resource ${resource.code} to menu ${menu.name}`);
      }
    }
  }

  // Grant all permissions to Super Admin role
  const superAdminRole = savedRoles.find(r => r.code === 'SUPER_ADMIN');
  if (superAdminRole) {
    for (const resource of savedResources) {
      const existingPermission = await roleMenuResourceRepository.findOne({
        where: {
          roleId: superAdminRole.id,
          resourceId: resource.id,
        },
      });

      if (!existingPermission) {
        const permission = roleMenuResourceRepository.create({
          roleId: superAdminRole.id,
          resourceId: resource.id,
          granted: true,
        });
        await roleMenuResourceRepository.save(permission);
      }
    }
    console.log(`Granted all permissions to Super Admin role`);
  }

  // Grant basic permissions to Admin role
  const adminRole = savedRoles.find(r => r.code === 'ADMIN');
  if (adminRole) {
    const adminPermissions = [
      'DASHBOARD',
      'USER_MANAGEMENT', 'USER_LIST', 'USER_VIEW', 'USER_CREATE', 'USER_UPDATE', 'USER_STATUS_TOGGLE',
      'DEPARTMENT_MANAGEMENT', 'DEPARTMENT_LIST', 'DEPARTMENT_VIEW', 'DEPARTMENT_CREATE', 'DEPARTMENT_UPDATE',
      'DICTIONARY_MANAGEMENT', 'DICTIONARY_LIST', 'DICTIONARY_VIEW', 'DICTIONARY_CREATE', 'DICTIONARY_UPDATE',
    ];

    for (const resourceCode of adminPermissions) {
      const resource = savedResources.find(r => r.code === resourceCode);
      if (resource) {
        const existingPermission = await roleMenuResourceRepository.findOne({
          where: {
            roleId: adminRole.id,
            resourceId: resource.id,
          },
        });

        if (!existingPermission) {
          const permission = roleMenuResourceRepository.create({
            roleId: adminRole.id,
            resourceId: resource.id,
            granted: true,
          });
          await roleMenuResourceRepository.save(permission);
        }
      }
    }
    console.log(`Granted basic permissions to Admin role`);
  }

  // Grant minimal permissions to User role
  const userRole = savedRoles.find(r => r.code === 'USER');
  if (userRole) {
    const userPermissions = ['DASHBOARD'];

    for (const resourceCode of userPermissions) {
      const resource = savedResources.find(r => r.code === resourceCode);
      if (resource) {
        const existingPermission = await roleMenuResourceRepository.findOne({
          where: {
            roleId: userRole.id,
            resourceId: resource.id,
          },
        });

        if (!existingPermission) {
          const permission = roleMenuResourceRepository.create({
            roleId: userRole.id,
            resourceId: resource.id,
            granted: true,
          });
          await roleMenuResourceRepository.save(permission);
        }
      }
    }
    console.log(`Granted minimal permissions to User role`);
  }

  console.log('RBAC data seeding completed!');
}
