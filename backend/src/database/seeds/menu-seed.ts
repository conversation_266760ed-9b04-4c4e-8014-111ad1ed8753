import { DataSource } from 'typeorm';
import { Menu } from '../../menu/domain/menu.entity';

/**
 * Seed default menu data
 * Creates the basic menu structure for the admin system
 */
export async function seedMenuData(dataSource: DataSource) {
  const menuRepository = dataSource.getRepository(Menu);

  console.log('Seeding menu data...');

  // Check if menus already exist
  const existingMenuCount = await menuRepository.count();
  if (existingMenuCount > 0) {
    console.log('Menus already exist, skipping menu creation');
    return;
  }

  // Define the menu structure
  const menuData = [
    // Root level menus
    {
      name: '首页',
      path: '/dashboard',
      component: 'Dashboard',
      icon: 'home',
      orderNum: 1,
      parentId: null,
      status: true,
      hidden: false,
      resourceCode: 'DASHBOARD'
    },
    {
      name: '系统管理',
      path: '/system',
      component: null,
      icon: 'settings',
      orderNum: 2,
      parentId: null,
      status: true,
      hidden: false,
      resourceCode: null // Parent menu doesn't need resource code
    },
    {
      name: '系统监控',
      path: '/monitor',
      component: null,
      icon: 'activity',
      orderNum: 3,
      parentId: null,
      status: true,
      hidden: false,
      resourceCode: null
    },
    {
      name: '系统工具',
      path: '/tool',
      component: null,
      icon: 'wrench',
      orderNum: 4,
      parentId: null,
      status: true,
      hidden: false,
      resourceCode: null
    }
  ];

  // Create root menus first
  const savedMenus = [];
  for (const menuItem of menuData) {
    const menu = menuRepository.create(menuItem);
    const savedMenu = await menuRepository.save(menu);
    savedMenus.push(savedMenu);
    console.log(`Created menu: ${savedMenu.name} (ID: ${savedMenu.id})`);
  }

  // Find parent menu IDs for submenus
  const systemMenu = savedMenus.find(m => m.path === '/system');
  const monitorMenu = savedMenus.find(m => m.path === '/monitor');
  const toolMenu = savedMenus.find(m => m.path === '/tool');

  // Define submenus
  const subMenuData = [
    // System management submenus
    {
      name: '用户管理',
      path: '/admin/users',
      component: 'UserManagement',
      icon: 'user',
      orderNum: 1,
      parentId: systemMenu.id,
      status: true,
      hidden: false,
      resourceCode: 'USER_MANAGEMENT'
    },
    {
      name: '角色管理',
      path: '/system/role',
      component: 'RoleManagement',
      icon: 'users',
      orderNum: 2,
      parentId: systemMenu.id,
      status: true,
      hidden: false,
      resourceCode: 'ROLE_MANAGEMENT'
    },
    {
      name: '菜单管理',
      path: '/system/menu',
      component: 'MenuManagement',
      icon: 'menu',
      orderNum: 3,
      parentId: systemMenu.id,
      status: true,
      hidden: false,
      resourceCode: 'MENU_MANAGEMENT'
    },
    {
      name: '部门管理',
      path: '/system/department',
      component: 'DepartmentManagement',
      icon: 'building',
      orderNum: 4,
      parentId: systemMenu.id,
      status: true,
      hidden: false,
      resourceCode: 'DEPARTMENT_MANAGEMENT'
    },
    {
      name: '字典管理',
      path: '/system/dictionary',
      component: 'DictionaryManagement',
      icon: 'book',
      orderNum: 5,
      parentId: systemMenu.id,
      status: true,
      hidden: false,
      resourceCode: 'DICTIONARY_MANAGEMENT'
    },

    // System monitoring submenus
    {
      name: '在线用户',
      path: '/monitor/online',
      component: 'OnlineUsers',
      icon: 'user-check',
      orderNum: 1,
      parentId: monitorMenu.id,
      status: true,
      hidden: false,
      resourceCode: 'ONLINE_USERS'
    },
    {
      name: '服务监控',
      path: '/monitor/server',
      component: 'ServerMonitor',
      icon: 'server',
      orderNum: 2,
      parentId: monitorMenu.id,
      status: true,
      hidden: false,
      resourceCode: 'SERVER_MONITOR'
    },
    {
      name: '操作日志',
      path: '/monitor/logs',
      component: 'OperationLogs',
      icon: 'file-text',
      orderNum: 3,
      parentId: monitorMenu.id,
      status: true,
      hidden: false,
      resourceCode: 'OPERATION_LOGS'
    },

    // System tools submenus
    {
      name: '代码生成',
      path: '/tool/gen',
      component: 'CodeGenerator',
      icon: 'code',
      orderNum: 1,
      parentId: toolMenu.id,
      status: true,
      hidden: false,
      resourceCode: 'CODE_GENERATOR'
    },
    {
      name: '系统接口',
      path: '/tool/swagger',
      component: 'SwaggerUI',
      icon: 'file-text',
      orderNum: 2,
      parentId: toolMenu.id,
      status: true,
      hidden: false,
      resourceCode: 'SWAGGER_UI'
    },
    {
      name: '数据备份',
      path: '/tool/backup',
      component: 'DataBackup',
      icon: 'database',
      orderNum: 3,
      parentId: toolMenu.id,
      status: true,
      hidden: false,
      resourceCode: 'DATA_BACKUP'
    }
  ];

  // Create submenus
  for (const subMenuItem of subMenuData) {
    const subMenu = menuRepository.create(subMenuItem);
    const savedSubMenu = await menuRepository.save(subMenu);
    console.log(`Created submenu: ${savedSubMenu.name} (ID: ${savedSubMenu.id}, Parent: ${savedSubMenu.parentId})`);
  }

  console.log('Menu data seeding completed!');
}
