import { DataSource } from 'typeorm';
import { User } from '../../user/domain/user.entity';
import { UserAuth } from '../../auth/domain/user-auth.entity';
import { Role } from '../../role/domain/role.entity';
import { AuthType } from '@common/enums/auth-type.enum';
import { LoginType } from '@common/enums/login-type.enum';
import { UserStatus } from '@common/enums/user-status.enum';
import * as bcrypt from 'bcryptjs';

/**
 * Seed default admin user account
 * Creates an admin user with both password and SMS verification auth methods
 */
export async function seedAdminUser(dataSource: DataSource) {
  const userRepository = dataSource.getRepository(User);
  const userAuthRepository = dataSource.getRepository(UserAuth);
  const roleRepository = dataSource.getRepository(Role);

  console.log('Seeding admin user...');

  // Admin user specifications
  const adminUsername = 'admin';
  const adminPassword = 'Qweqwe';
  const adminPhone = '***********'; // Default admin phone for SMS verification

  // Check if admin user already exists
  let adminUser = await userRepository.findOne({
    where: { username: adminUsername },
    relations: ['role', 'authMethods'],
  });

  if (adminUser) {
    console.log('Admin user already exists, skipping creation');
    return;
  }

  // Get SUPER_ADMIN role
  const superAdminRole = await roleRepository.findOne({
    where: { code: 'SUPER_ADMIN' },
  });

  if (!superAdminRole) {
    throw new Error('SUPER_ADMIN role not found. Please run RBAC seeding first.');
  }

  // Create admin user
  adminUser = new User();
  adminUser.username = adminUsername;
  adminUser.email = '<EMAIL>';
  adminUser.phone = adminPhone;
  adminUser.avatar = 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin';
  adminUser.status = UserStatus.ACTIVE;
  adminUser.loginType = LoginType.PASSWORD;
  adminUser.role = superAdminRole;
  adminUser.roleId = superAdminRole.id;

  // Save admin user
  adminUser = await userRepository.save(adminUser);
  console.log(`Created admin user: ${adminUser.username} (ID: ${adminUser.id})`);

  // Hash the password
  const salt = await bcrypt.genSalt();
  const hashedPassword = await bcrypt.hash(adminPassword, salt);

  // Create password authentication method
  const passwordAuth = new UserAuth();
  passwordAuth.user = adminUser;
  passwordAuth.authType = AuthType.PASSWORD;
  passwordAuth.account = adminUsername;
  passwordAuth.credential = hashedPassword;

  await userAuthRepository.save(passwordAuth);
  console.log('Created password authentication method for admin user');

  // Create SMS verification authentication method
  const smsAuth = new UserAuth();
  smsAuth.user = adminUser;
  smsAuth.authType = AuthType.SMS_VERIFICATION;
  smsAuth.account = adminPhone;
  smsAuth.credential = ''; // Will be set when SMS code is sent

  await userAuthRepository.save(smsAuth);
  console.log('Created SMS verification authentication method for admin user');

  console.log('Admin user seeding completed!');
  console.log('Admin credentials:');
  console.log(`  Username: ${adminUsername}`);
  console.log(`  Password: ${adminPassword}`);
  console.log(`  Phone: ${adminPhone} (for SMS verification)`);
  console.log(`  Role: ${superAdminRole.name} (${superAdminRole.code})`);
}
