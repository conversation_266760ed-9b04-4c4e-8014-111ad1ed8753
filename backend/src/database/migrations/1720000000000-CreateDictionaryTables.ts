import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateDictionaryTables1720000000000 implements MigrationInterface {
    name = 'CreateDictionaryTables1720000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create dictionary_entries table
        await queryRunner.query(`
            CREATE TABLE "dictionary_entries" (
                "id" SERIAL NOT NULL,
                "key" character varying NOT NULL,
                "description" character varying,
                "enabled" boolean NOT NULL DEFAULT true,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_dictionary_entries_key" UNIQUE ("key"),
                CONSTRAINT "PK_dictionary_entries" PRIMARY KEY ("id")
            )
        `);

        // Create dictionary_values table
        await queryRunner.query(`
            CREATE TABLE "dictionary_values" (
                "id" SERIAL NOT NULL,
                "value" character varying NOT NULL,
                "label" character varying,
                "order_num" integer NOT NULL DEFAULT 0,
                "entry_id" integer NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_dictionary_values" PRIMARY KEY ("id")
            )
        `);

        // Add foreign key constraint
        await queryRunner.query(`
            ALTER TABLE "dictionary_values" 
            ADD CONSTRAINT "FK_dictionary_values_entry_id" 
            FOREIGN KEY ("entry_id") 
            REFERENCES "dictionary_entries"("id") 
            ON DELETE CASCADE 
            ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraint
        await queryRunner.query(`
            ALTER TABLE "dictionary_values" 
            DROP CONSTRAINT "FK_dictionary_values_entry_id"
        `);

        // Drop tables
        await queryRunner.query(`DROP TABLE "dictionary_values"`);
        await queryRunner.query(`DROP TABLE "dictionary_entries"`);
    }
}
