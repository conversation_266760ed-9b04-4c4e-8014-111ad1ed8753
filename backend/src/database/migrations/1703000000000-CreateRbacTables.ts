import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateRbacTables1703000000000 implements MigrationInterface {
  name = 'CreateRbacTables1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create roles table
    await queryRunner.query(`
      CREATE TABLE "roles" (
        "id" SERIAL NOT NULL,
        "name" character varying NOT NULL,
        "description" character varying,
        "code" character varying NOT NULL,
        "status" boolean NOT NULL DEFAULT true,
        "order_num" integer NOT NULL DEFAULT 0,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_roles_name" UNIQUE ("name"),
        CONSTRAINT "UQ_roles_code" UNIQUE ("code"),
        CONSTRAINT "PK_roles" PRIMARY KEY ("id")
      )
    `);

    // Create resource_type enum
    await queryRunner.query(`
      CREATE TYPE "resources_type_enum" AS ENUM('menu', 'button', 'api', 'page')
    `);

    // Create resources table
    await queryRunner.query(`
      CREATE TABLE "resources" (
        "id" SERIAL NOT NULL,
        "code" character varying NOT NULL,
        "name" character varying NOT NULL,
        "description" character varying,
        "type" "resources_type_enum" NOT NULL DEFAULT 'menu',
        "menu_id" integer,
        "api_path" character varying,
        "api_method" character varying,
        "status" boolean NOT NULL DEFAULT true,
        "order_num" integer NOT NULL DEFAULT 0,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_resources_code" UNIQUE ("code"),
        CONSTRAINT "PK_resources" PRIMARY KEY ("id")
      )
    `);

    // Create role_menu_resources table
    await queryRunner.query(`
      CREATE TABLE "role_menu_resources" (
        "id" SERIAL NOT NULL,
        "role_id" integer NOT NULL,
        "menu_id" integer,
        "resource_id" integer NOT NULL,
        "granted" boolean NOT NULL DEFAULT true,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "UQ_role_menu_resource" UNIQUE ("role_id", "menu_id", "resource_id"),
        CONSTRAINT "PK_role_menu_resources" PRIMARY KEY ("id")
      )
    `);

    // Add role_id column to users table
    await queryRunner.query(`
      ALTER TABLE "users" ADD "role_id" integer
    `);

    // Add resource_code column to menus table if it doesn't exist
    await queryRunner.query(`
      ALTER TABLE "menus" ADD COLUMN IF NOT EXISTS "resource_code" character varying
    `);

    await queryRunner.query(`
      ALTER TABLE "menus" ADD CONSTRAINT "UQ_menus_resource_code" UNIQUE ("resource_code")
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "resources" 
      ADD CONSTRAINT "FK_resources_menu" 
      FOREIGN KEY ("menu_id") REFERENCES "menus"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "role_menu_resources" 
      ADD CONSTRAINT "FK_role_menu_resources_role" 
      FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "role_menu_resources" 
      ADD CONSTRAINT "FK_role_menu_resources_menu" 
      FOREIGN KEY ("menu_id") REFERENCES "menus"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "role_menu_resources" 
      ADD CONSTRAINT "FK_role_menu_resources_resource" 
      FOREIGN KEY ("resource_id") REFERENCES "resources"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "users" 
      ADD CONSTRAINT "FK_users_role" 
      FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    // Insert default roles
    await queryRunner.query(`
      INSERT INTO "roles" ("name", "description", "code", "status", "order_num") VALUES
      ('Super Admin', 'System super administrator with all permissions', 'SUPER_ADMIN', true, 1),
      ('Admin', 'System administrator', 'ADMIN', true, 2),
      ('User', 'Regular user', 'USER', true, 3)
    `);

    // Insert default resources for existing menus
    await queryRunner.query(`
      INSERT INTO "resources" ("code", "name", "description", "type", "status", "order_num") VALUES
      ('DASHBOARD', 'Dashboard Access', 'Access to dashboard', 'menu', true, 1),
      ('USER_MANAGEMENT', 'User Management', 'Access to user management', 'menu', true, 2),
      ('USER_LIST', 'User List', 'View user list', 'button', true, 3),
      ('USER_CREATE', 'Create User', 'Create new user', 'button', true, 4),
      ('USER_UPDATE', 'Update User', 'Update user information', 'button', true, 5),
      ('USER_DELETE', 'Delete User', 'Delete user', 'button', true, 6),
      ('ROLE_MANAGEMENT', 'Role Management', 'Access to role management', 'menu', true, 7),
      ('ROLE_LIST', 'Role List', 'View role list', 'button', true, 8),
      ('ROLE_CREATE', 'Create Role', 'Create new role', 'button', true, 9),
      ('ROLE_UPDATE', 'Update Role', 'Update role information', 'button', true, 10),
      ('ROLE_DELETE', 'Delete Role', 'Delete role', 'button', true, 11),
      ('PERMISSION_MANAGEMENT', 'Permission Management', 'Access to permission management', 'menu', true, 12),
      ('PERMISSION_ASSIGN', 'Assign Permission', 'Assign permissions to roles', 'button', true, 13),
      ('PERMISSION_VIEW', 'View Permission', 'View permissions', 'button', true, 14),
      ('MENU_MANAGEMENT', 'Menu Management', 'Access to menu management', 'menu', true, 15),
      ('MENU_LIST', 'Menu List', 'View menu list', 'button', true, 16),
      ('MENU_CREATE', 'Create Menu', 'Create new menu', 'button', true, 17),
      ('MENU_UPDATE', 'Update Menu', 'Update menu information', 'button', true, 18),
      ('MENU_DELETE', 'Delete Menu', 'Delete menu', 'button', true, 19),
      ('RESOURCE_MANAGEMENT', 'Resource Management', 'Access to resource management', 'menu', true, 20),
      ('RESOURCE_LIST', 'Resource List', 'View resource list', 'button', true, 21),
      ('RESOURCE_CREATE', 'Create Resource', 'Create new resource', 'button', true, 22),
      ('RESOURCE_UPDATE', 'Update Resource', 'Update resource information', 'button', true, 23),
      ('RESOURCE_DELETE', 'Delete Resource', 'Delete resource', 'button', true, 24),
      ('RESOURCE_VIEW', 'View Resource', 'View resource details', 'button', true, 25)
    `);

    // Grant all permissions to Super Admin role
    await queryRunner.query(`
      INSERT INTO "role_menu_resources" ("role_id", "resource_id", "granted")
      SELECT 1, "id", true FROM "resources"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraints
    await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_users_role"`);
    await queryRunner.query(`ALTER TABLE "role_menu_resources" DROP CONSTRAINT "FK_role_menu_resources_resource"`);
    await queryRunner.query(`ALTER TABLE "role_menu_resources" DROP CONSTRAINT "FK_role_menu_resources_menu"`);
    await queryRunner.query(`ALTER TABLE "role_menu_resources" DROP CONSTRAINT "FK_role_menu_resources_role"`);
    await queryRunner.query(`ALTER TABLE "resources" DROP CONSTRAINT "FK_resources_menu"`);

    // Remove columns
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "role_id"`);
    await queryRunner.query(`ALTER TABLE "menus" DROP CONSTRAINT "UQ_menus_resource_code"`);
    await queryRunner.query(`ALTER TABLE "menus" DROP COLUMN "resource_code"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "role_menu_resources"`);
    await queryRunner.query(`DROP TABLE "resources"`);
    await queryRunner.query(`DROP TABLE "roles"`);

    // Drop enum
    await queryRunner.query(`DROP TYPE "resources_type_enum"`);
  }
}
