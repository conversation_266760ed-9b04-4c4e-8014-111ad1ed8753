import { DataSource } from 'typeorm';
import { config } from 'dotenv';
import { User } from '../user/domain/user.entity';
import { UserAuth } from '../auth/domain/user-auth.entity';
import { Menu } from '../menu/domain/menu.entity';
import { Role } from '../role/domain/role.entity';
import { Resource } from '../resource/domain/resource.entity';
import { RoleMenuResource } from '../rbac/domain/role-menu-resource.entity';
import { SnakeCaseNamingStrategy } from './snake-case-naming.strategy';
import { MenuService } from '../menu/menu.service';

// Load environment variables
config();

const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || '**************',
  port: parseInt(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'demo_shop_user',
  password: process.env.DB_PASSWORD || 'Demo_shop@2024_Secure',
  database: process.env.DB_DATABASE || 'demo_shop',
  entities: [User, UserAuth, Menu, Role, Resource, RoleMenuResource],
  namingStrategy: new SnakeCaseNamingStrategy(),
});

/**
 * Test script to verify admin user menu access
 */
async function testAdminMenuAccess() {
  try {
    console.log('Initializing database connection...');
    await dataSource.initialize();
    console.log('Database connection established.');

    const userRepository = dataSource.getRepository(User);
    const menuRepository = dataSource.getRepository(Menu);
    const roleMenuResourceRepository = dataSource.getRepository(RoleMenuResource);

    // Find admin user
    const adminUser = await userRepository.findOne({
      where: { username: 'admin' },
      relations: ['role'],
    });

    if (!adminUser) {
      console.error('❌ Admin user not found! Please run seeding first.');
      return;
    }

    console.log(`✅ Found admin user: ${adminUser.username}`);
    console.log(`✅ Admin role: ${adminUser.role?.name} (${adminUser.role?.code})`);

    // Check total menus in database
    const totalMenus = await menuRepository.count();
    console.log(`📊 Total menus in database: ${totalMenus}`);

    // Check admin permissions
    const adminPermissions = await roleMenuResourceRepository.find({
      where: { roleId: adminUser.role.id, granted: true },
      relations: ['resource', 'menu'],
    });

    console.log(`🔑 Admin has ${adminPermissions.length} permissions`);

    // Get menu IDs that admin has access to (from resource.menuId, not permission.menuId)
    const accessibleMenuIds = [...new Set(adminPermissions.map(p => p.resource.menuId).filter(id => id))];
    console.log(`📋 Admin has access to ${accessibleMenuIds.length} menus`);

    // Test MenuService getUserMenuTree
    const menuService = new MenuService(
      menuRepository,
      userRepository,
      roleMenuResourceRepository
    );

    const userMenuTree = await menuService.getUserMenuTree(adminUser.id);
    console.log(`🌳 User menu tree returned ${userMenuTree.length} root menus`);

    // Display the menu tree structure
    const displayMenuTree = (menus: any[], level = 0) => {
      for (const menu of menus) {
        const indent = '  '.repeat(level);
        console.log(`${indent}📁 ${menu.name} (${menu.path}) [ID: ${menu.id}]`);
        if (menu.children && menu.children.length > 0) {
          displayMenuTree(menu.children, level + 1);
        }
      }
    };

    if (userMenuTree.length > 0) {
      console.log('\n🎯 Admin User Menu Tree:');
      displayMenuTree(userMenuTree);
    } else {
      console.log('❌ No menus returned for admin user!');

      // Debug information
      console.log('\n🔍 Debug Information:');
      console.log('Admin permissions with menu details:');
      for (const perm of adminPermissions.slice(0, 10)) { // Show first 10
        console.log(`  - Resource: ${perm.resource.code}, Resource Menu ID: ${perm.resource.menuId}, Permission Menu ID: ${perm.menuId}, Menu: ${perm.menu?.name || 'N/A'}`);
      }
    }

    console.log('\n✅ Test completed successfully!');
  } catch (error) {
    console.error('❌ Error testing admin menu access:', error);
  } finally {
    await dataSource.destroy();
    console.log('Database connection closed.');
  }
}

testAdminMenuAccess();
