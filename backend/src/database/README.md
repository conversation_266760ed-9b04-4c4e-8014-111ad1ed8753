# Database Naming Strategy

This directory contains the configuration for TypeORM's naming strategy.

## Snake Case Naming Strategy

The `SnakeCaseNamingStrategy` class in `snake-case-naming.strategy.ts` implements TypeORM's `NamingStrategyInterface` to automatically convert camelCase property names to snake_case column names in the database.

### Features

- Automatically converts all entity property names from camelCase to snake_case in the database
- Applies to all database operations (DML/DQL)
- Applies to all Atlas migrations
- Provides consistent naming conventions across the entire database
- Eliminates the need for explicit column name declarations in entity files

### Implementation

The strategy is applied in four places:

1. In `data-source.ts` for TypeORM's DataSource configuration
2. In `load.ts` for TypeORM entity loading
3. In `atlas-provider.js` for Atlas migration schema generation with snake_case column names
4. In `scripts/generate-migration.js` for automatic migration file creation

### Usage

No special action is needed in entity files. Simply define your entity properties in camelCase, and they will be automatically converted to snake_case in the database.

Example:

```typescript
@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  firstName: string; // Will be stored as "first_name" in the database

  @Column()
  lastLoginDate: Date; // Will be stored as "last_login_date" in the database
}
```

### Migration Commands

To generate and apply migrations that use snake_case column names, you can use either the combined command or the individual commands:

#### Combined Migration Command (Recommended)

```bash
npm run migration         # Runs all migration steps in sequence
npm run migration --dry-run  # Preview migration changes without applying them
```

This single command automatically:
1. Generates schema.sql with snake_case columns
2. Creates a new migration based on the schema differences
3. Applies the migration to the database (or simulates application with --dry-run)

#### Individual Migration Commands

If you need more control, you can still run the individual commands:

```bash
npm run migration:schema   # Generate schema.sql with snake_case columns
npm run migration:create   # Create a new migration file from TypeORM entities
npm run migration:generate # Generate a migration using Atlas diff
npm run migration:apply    # Apply migrations to the database
```

All these commands ensure that migrations use the snake_case naming convention.

### Automatic Table Creation

The migration workflow has been enhanced to automatically create tables from TypeORM entities:

1. When the database is empty, it creates an initial migration with all table creation statements
2. When the database already has tables, it uses Atlas diff to generate incremental migrations
3. All column names are automatically converted to snake_case
4. Foreign key constraints, unique constraints, and references are properly handled

### Custom Atlas Provider

We've implemented a custom Atlas provider script (`atlas-provider.js`) that:

1. Uses TypeORM to generate the database schema
2. Post-processes the SQL to ensure all column names are in snake_case
3. Ensures foreign key constraints also use snake_case column names

This approach guarantees that all database operations and migrations consistently use snake_case naming conventions, even if the TypeORM naming strategy doesn't fully apply to all aspects of schema generation.
