import { Injectable, CanActivate, ExecutionContext, SetMetadata } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RbacService } from '../rbac.service';

export const RESOURCE_CODE_KEY = 'resourceCode';
export const RequirePermission = (resourceCode: string) => SetMetadata(RESOURCE_CODE_KEY, resourceCode);

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private rbacService: RbacService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const resourceCode = this.reflector.getAllAndOverride<string>(RESOURCE_CODE_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!resourceCode) {
      return true; // No permission required
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return false;
    }

    return this.rbacService.checkPermission(user.id, resourceCode);
  }
}
