import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RoleMenuResource } from './domain/role-menu-resource.entity';
import { Role } from '../role/domain/role.entity';
import { Resource } from '../resource/domain/resource.entity';
import { Menu } from '../menu/domain/menu.entity';
import { User } from '../user/domain/user.entity';
import { RbacService } from './rbac.service';
import { RbacController } from './rbac.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([RoleMenuResource, Role, Resource, Menu, User]),
  ],
  providers: [RbacService],
  controllers: [RbacController],
  exports: [RbacService],
})
export class RbacModule {}
