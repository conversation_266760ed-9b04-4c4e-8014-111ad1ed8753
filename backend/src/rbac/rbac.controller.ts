import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RbacService, PermissionAssignmentDto } from './rbac.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@ApiTags('rbac')
@Controller('rbac')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RbacController {
  constructor(private readonly rbacService: RbacService) {}

  @Post('assign-permissions')
  @ApiOperation({ summary: 'Assign permissions to a role' })
  @ApiResponse({ status: 200, description: 'Permissions assigned successfully' })
  assignPermissions(@Body() assignment: PermissionAssignmentDto) {
    return this.rbacService.assignPermissions(assignment);
  }

  @Get('role/:roleId/permissions')
  @ApiOperation({ summary: 'Get role permissions for a specific menu' })
  @ApiResponse({ status: 200, description: 'Role permissions retrieved successfully' })
  getRoleMenuPermissions(
    @Param('roleId') roleId: string,
    @Query('menuId') menuId?: string,
  ) {
    return this.rbacService.getRoleMenuPermissions(+roleId, menuId ? +menuId : undefined);
  }

  @Get('menu/:menuId/resources')
  @ApiOperation({ summary: 'Get all resources for a menu' })
  @ApiResponse({ status: 200, description: 'Menu resources retrieved successfully' })
  getMenuResources(@Param('menuId') menuId: string) {
    return this.rbacService.getMenuResources(+menuId);
  }

  @Get('user/:userId/permissions')
  @ApiOperation({ summary: 'Get user permissions' })
  @ApiResponse({ status: 200, description: 'User permissions retrieved successfully' })
  getUserPermissions(@Param('userId') userId: string) {
    return this.rbacService.getUserPermissions(+userId);
  }

  @Get('user/:userId/menus')
  @ApiOperation({ summary: 'Get user accessible menus' })
  @ApiResponse({ status: 200, description: 'User menus retrieved successfully' })
  getUserMenus(@Param('userId') userId: string) {
    return this.rbacService.getUserMenus(+userId);
  }

  @Get('user/:userId/menu-tree')
  @ApiOperation({ summary: 'Get user menu tree with permission filtering' })
  @ApiResponse({ status: 200, description: 'User menu tree retrieved successfully' })
  getUserMenuTree(@Param('userId') userId: string) {
    return this.rbacService.buildPermissionMenuTree(+userId);
  }
}
