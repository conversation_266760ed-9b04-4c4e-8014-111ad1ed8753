import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Unique } from 'typeorm';
import { Role } from '../../role/domain/role.entity';
import { Menu } from '../../menu/domain/menu.entity';
import { Resource } from '../../resource/domain/resource.entity';

@Entity('role_menu_resources')
@Unique(['roleId', 'menuId', 'resourceId'])
export class RoleMenuResource {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  roleId: number;

  @ManyToOne(() => Role, role => role.roleMenuResources, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @Column({ nullable: true })
  menuId: number;

  @ManyToOne(() => Menu, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'menu_id' })
  menu: Menu;

  @Column()
  resourceId: number;

  @ManyToOne(() => Resource, resource => resource.roleMenuResources, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'resource_id' })
  resource: Resource;

  @Column({ default: true })
  granted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
