import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Adapter, Model, Helper } from 'casbin';
import { RoleMenuResource } from '../domain/role-menu-resource.entity';
import { Role } from '../../role/domain/role.entity';
import { Resource } from '../../resource/domain/resource.entity';

@Injectable()
export class CasbinAdapter implements Adapter {
  constructor(
    @InjectRepository(RoleMenuResource)
    private roleMenuResourceRepository: Repository<RoleMenuResource>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Resource)
    private resourceRepository: Repository<Resource>,
  ) {}

  async loadPolicy(model: Model): Promise<void> {
    const policies = await this.roleMenuResourceRepository.find({
      relations: ['role', 'resource'],
      where: { granted: true },
    });

    for (const policy of policies) {
      const line = `p, ${policy.role.code}, ${policy.resource.code}, read`;
      Helper.loadPolicyLine(line, model);
    }

    // Load role inheritance (if needed)
    const roles = await this.roleRepository.find();
    for (const role of roles) {
      // For now, we don't have role inheritance, but this is where you'd add it
      // Example: Helper.loadPolicyLine(`g, user:${userId}, ${role.code}`, model);
    }
  }

  async savePolicy(model: Model): Promise<boolean> {
    // Clear existing policies
    await this.roleMenuResourceRepository.delete({});

    // Save new policies
    const policies = model.model.get('p').get('p').policy;
    for (const policy of policies) {
      const [, roleCode, resourceCode] = policy;
      
      const role = await this.roleRepository.findOne({ where: { code: roleCode } });
      const resource = await this.resourceRepository.findOne({ where: { code: resourceCode } });
      
      if (role && resource) {
        const roleMenuResource = new RoleMenuResource();
        roleMenuResource.role = role;
        roleMenuResource.resource = resource;
        roleMenuResource.granted = true;
        
        await this.roleMenuResourceRepository.save(roleMenuResource);
      }
    }

    return true;
  }

  async addPolicy(sec: string, ptype: string, rule: string[]): Promise<void> {
    // Implementation for adding a single policy
  }

  async removePolicy(sec: string, ptype: string, rule: string[]): Promise<void> {
    // Implementation for removing a single policy
  }

  async removeFilteredPolicy(sec: string, ptype: string, fieldIndex: number, ...fieldValues: string[]): Promise<void> {
    // Implementation for removing filtered policies
  }
}
