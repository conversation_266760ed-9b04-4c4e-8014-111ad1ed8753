import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { RoleMenuResource } from './domain/role-menu-resource.entity';
import { Role } from '../role/domain/role.entity';
import { Resource } from '../resource/domain/resource.entity';
import { Menu } from '../menu/domain/menu.entity';
import { User } from '../user/domain/user.entity';

export interface PermissionAssignmentDto {
  roleId: number;
  menuId?: number;
  resourceIds: number[];
}

export interface UserPermissionsDto {
  userId: number;
  permissions: string[];
  menus: Menu[];
}

@Injectable()
export class RbacService {
  constructor(
    @InjectRepository(RoleMenuResource)
    private roleMenuResourceRepository: Repository<RoleMenuResource>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Resource)
    private resourceRepository: Repository<Resource>,
    @InjectRepository(Menu)
    private menuRepository: Repository<Menu>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  /**
   * Check if user has permission to access a resource
   */
  async checkPermission(userId: number, resourceCode: string, action: string = 'read'): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role'],
    });

    if (!user || !user.role) {
      return false;
    }

    // Find the resource by code
    const resource = await this.resourceRepository.findOne({
      where: { code: resourceCode },
    });

    if (!resource) {
      return false;
    }

    // Check if the user's role has permission for this resource
    const permission = await this.roleMenuResourceRepository.findOne({
      where: {
        roleId: user.role.id,
        resourceId: resource.id,
        granted: true,
      },
    });

    return !!permission;
  }

  /**
   * Get user's accessible menus based on permissions
   */
  async getUserMenus(userId: number): Promise<Menu[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role'],
    });

    if (!user || !user.role) {
      return [];
    }

    // Get all permissions for the user's role
    const permissions = await this.roleMenuResourceRepository.find({
      where: { roleId: user.role.id, granted: true },
      relations: ['menu', 'resource'],
    });

    // Extract unique menus
    const menuIds = [...new Set(permissions.map(p => p.menuId).filter(id => id))];

    if (menuIds.length === 0) {
      return [];
    }

    return this.menuRepository.find({
      where: { id: In(menuIds), status: true },
      order: { orderNum: 'ASC' },
    });
  }

  /**
   * Get user's permissions
   */
  async getUserPermissions(userId: number): Promise<string[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role'],
    });

    if (!user || !user.role) {
      return [];
    }

    const permissions = await this.roleMenuResourceRepository.find({
      where: { roleId: user.role.id, granted: true },
      relations: ['resource'],
    });

    return permissions.map(p => p.resource.code);
  }

  /**
   * Assign permissions to a role
   */
  async assignPermissions(assignment: PermissionAssignmentDto): Promise<void> {
    const { roleId, menuId, resourceIds } = assignment;

    // Remove existing permissions for this role and menu
    if (menuId) {
      await this.roleMenuResourceRepository.delete({ roleId, menuId });
    } else {
      await this.roleMenuResourceRepository.delete({ roleId });
    }

    // Add new permissions
    for (const resourceId of resourceIds) {
      const roleMenuResource = new RoleMenuResource();
      roleMenuResource.roleId = roleId;
      roleMenuResource.menuId = menuId;
      roleMenuResource.resourceId = resourceId;
      roleMenuResource.granted = true;

      await this.roleMenuResourceRepository.save(roleMenuResource);
    }

    // Policies updated successfully
  }

  /**
   * Get role permissions for a specific menu
   */
  async getRoleMenuPermissions(roleId: number, menuId?: number): Promise<RoleMenuResource[]> {
    const where: any = { roleId, granted: true };
    if (menuId) {
      where.menuId = menuId;
    }

    return this.roleMenuResourceRepository.find({
      where,
      relations: ['menu', 'resource'],
    });
  }

  /**
   * Get all resources for a menu
   */
  async getMenuResources(menuId: number): Promise<Resource[]> {
    return this.resourceRepository.find({
      where: { menuId, status: true },
      order: { orderNum: 'ASC' },
    });
  }

  /**
   * Build menu tree with permission filtering
   */
  async buildPermissionMenuTree(userId: number): Promise<Menu[]> {
    const accessibleMenus = await this.getUserMenus(userId);
    const allMenus = await this.menuRepository.find({
      where: { status: true },
      order: { orderNum: 'ASC' },
    });

    // Filter menus based on permissions
    const accessibleMenuIds = new Set(accessibleMenus.map(m => m.id));

    // Build tree structure
    const buildTree = (menus: Menu[], parentId: number = null): Menu[] => {
      const result = [];
      for (const menu of menus) {
        if ((menu.parentId === parentId) || (!menu.parentId && !parentId)) {
          if (accessibleMenuIds.has(menu.id)) {
            const children = buildTree(menus, menu.id);
            if (children.length || !menu.parentId) {
              menu.children = children;
              result.push(menu);
            }
          }
        }
      }
      return result;
    };

    return buildTree(allMenus);
  }
}


