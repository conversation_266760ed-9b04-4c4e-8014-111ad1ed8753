import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, FindOptionsWhere } from 'typeorm';
import { User } from './domain/user.entity';
import { UserQueryDto } from '../dto/user-query.dto';
import { UserStatus } from '@common/enums/user-status.enum';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async findAll(queryDto: UserQueryDto) {
    const {
      username,
      email,
      phone,
      status,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      page = 1,
      limit = 10
    } = queryDto;

    const where: FindOptionsWhere<User> = {};

    if (username) {
      where.username = Like(`%${username}%`);
    }

    if (email) {
      where.email = Like(`%${email}%`);
    }

    if (phone) {
      where.phone = Like(`%${phone}%`);
    }

    if (status) {
      where.status = status;
    }

    const [users, total] = await this.userRepository.findAndCount({
      where,
      relations: ['role'],
      order: {
        [sortBy]: sortOrder,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: users,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    return this.userRepository.findOne({
      where: { id },
      relations: ['role']
    });
  }

  async findByUsername(username: string) {
    return this.userRepository.findOne({
      where: { username },
      relations: ['role']
    });
  }

  async updateStatus(id: number, status: UserStatus) {
    const user = await this.findOne(id);
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    user.status = status;
    await this.userRepository.save(user);

    return {
      message: 'User status updated successfully',
      user: {
        id: user.id,
        username: user.username,
        status: user.status,
      },
    };
  }
}
