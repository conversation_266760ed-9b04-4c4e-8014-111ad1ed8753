import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { IUser } from '@common/interfaces/user.interface';
import { UserStatus } from '@common/enums/user-status.enum';
import { LoginType } from '@common/enums/login-type.enum';
import { UserAuth } from '../../auth/domain/user-auth.entity';
import { Role } from '../../role/domain/role.entity';

@Entity('users')
export class User implements IUser {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  username: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true })
  token: string;

  @Column({ nullable: true })
  avatar: string;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
  })
  status: UserStatus;

  @Column({
    type: 'enum',
    enum: LoginType,
    default: LoginType.PASSWORD,
  })
  loginType: LoginType;

  @Column({ nullable: true })
  thirdPartyId: string;

  @Column({ nullable: true })
  refreshToken: string;

  @Column({ type: 'timestamp', nullable: true })
  refreshTokenExpiresAt: Date;

  @OneToMany(() => UserAuth, userAuth => userAuth.user)
  authMethods: UserAuth[];

  @Column({ nullable: true })
  roleId: number;

  @ManyToOne(() => Role, role => role.users, { nullable: true })
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
