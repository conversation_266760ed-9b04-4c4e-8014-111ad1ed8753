import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role } from './domain/role.entity';

export interface CreateRoleDto {
  name: string;
  description?: string;
  code: string;
  status?: boolean;
  orderNum?: number;
}

export interface UpdateRoleDto {
  name?: string;
  description?: string;
  code?: string;
  status?: boolean;
  orderNum?: number;
}

export interface RoleQueryDto {
  page?: number;
  limit?: number;
  name?: string;
  code?: string;
  status?: boolean;
}

@Injectable()
export class RoleService {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
  ) {}

  /**
   * Create a new role
   */
  async create(createRoleDto: CreateRoleDto): Promise<Role> {
    // Check if role with same name or code already exists
    const existingRole = await this.roleRepository.findOne({
      where: [
        { name: createRoleDto.name },
        { code: createRoleDto.code }
      ],
    });

    if (existingRole) {
      throw new ConflictException('Role with this name or code already exists');
    }

    const role = this.roleRepository.create(createRoleDto);
    return this.roleRepository.save(role);
  }

  /**
   * Find all roles with pagination and filtering
   */
  async findAll(query: RoleQueryDto): Promise<{ data: Role[]; total: number }> {
    const { page = 1, limit = 10, name, code, status } = query;
    const queryBuilder = this.roleRepository.createQueryBuilder('role');

    if (name) {
      queryBuilder.andWhere('role.name ILIKE :name', { name: `%${name}%` });
    }

    if (code) {
      queryBuilder.andWhere('role.code ILIKE :code', { code: `%${code}%` });
    }

    if (status !== undefined) {
      queryBuilder.andWhere('role.status = :status', { status });
    }

    queryBuilder
      .orderBy('role.orderNum', 'ASC')
      .addOrderBy('role.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    return { data, total };
  }

  /**
   * Find role by ID
   */
  async findOne(id: number): Promise<Role> {
    const role = await this.roleRepository.findOne({
      where: { id },
      relations: ['users'],
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }

    return role;
  }

  /**
   * Update role
   */
  async update(id: number, updateRoleDto: UpdateRoleDto): Promise<Role> {
    const role = await this.findOne(id);

    // Check if updating name or code conflicts with existing roles
    if (updateRoleDto.name || updateRoleDto.code) {
      const conflictConditions = [];
      if (updateRoleDto.name) {
        conflictConditions.push({ name: updateRoleDto.name });
      }
      if (updateRoleDto.code) {
        conflictConditions.push({ code: updateRoleDto.code });
      }

      const existingRole = await this.roleRepository.findOne({
        where: conflictConditions,
      });

      if (existingRole && existingRole.id !== id) {
        throw new ConflictException('Role with this name or code already exists');
      }
    }

    Object.assign(role, updateRoleDto);
    return this.roleRepository.save(role);
  }

  /**
   * Delete role
   */
  async remove(id: number): Promise<void> {
    const role = await this.findOne(id);
    
    // Check if role has users assigned
    if (role.users && role.users.length > 0) {
      throw new ConflictException('Cannot delete role that has users assigned');
    }

    await this.roleRepository.remove(role);
  }

  /**
   * Get all active roles for dropdown/selection
   */
  async getActiveRoles(): Promise<Role[]> {
    return this.roleRepository.find({
      where: { status: true },
      order: { orderNum: 'ASC', name: 'ASC' },
    });
  }
}
