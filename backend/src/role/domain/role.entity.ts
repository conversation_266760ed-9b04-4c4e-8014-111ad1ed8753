import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToMany, JoinTable } from 'typeorm';
import { User } from '../../user/domain/user.entity';
import { RoleMenuResource } from '../../rbac/domain/role-menu-resource.entity';

@Entity('roles')
export class Role {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ unique: true })
  code: string;

  @Column({ default: true })
  status: boolean;

  @Column({ default: 0 })
  orderNum: number;

  @OneToMany(() => User, user => user.role)
  users: User[];

  @OneToMany(() => RoleMenuResource, roleMenuResource => roleMenuResource.role)
  roleMenuResources: RoleMenuResource[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
