import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import * as fs from 'fs';
import * as path from 'path';

async function bootstrap() {
  try {
    // 创建 NestJS 应用
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn'], // 只显示错误和警告，减少日志输出
    });
    app.useGlobalPipes(new ValidationPipe({
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
      whitelist: true,
      forbidNonWhitelisted: false,
    }));
    app.setGlobalPrefix('api', { exclude: ['/'] });

    // 创建 Swagger 文档
    const config = new DocumentBuilder()
      .setTitle('Mini Shop API')
      .setDescription('The Mini Shop API documentation')
      .setVersion('1.0')
      .addTag('users')
      .addTag('auth')
      .addTag('sms')
      .addTag('menus')
      .addTag('departments')
      .addTag('dictionary')
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, config, {
      extraModels: [],
      deepScanRoutes: true,
    });

    // 保存 OpenAPI 文档到文件（JSON 格式）
    const outputPath = path.resolve(process.cwd(), '..', 'openapi.json');

    // 确保目录存在
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // 将 OpenAPI 文档保存为 JSON 格式
    fs.writeFileSync(outputPath, JSON.stringify(document, null, 2));
    console.log(`OpenAPI 文档已保存到 ${outputPath}`);

    // 关闭应用
    await app.close();

    console.log('OpenAPI 文档生成成功！');
    process.exit(0);
  } catch (error) {
    console.error('生成 OpenAPI 文档时出错:', error);
    process.exit(1);
  }
}

bootstrap();
