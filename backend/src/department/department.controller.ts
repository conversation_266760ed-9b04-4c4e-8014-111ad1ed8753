import { Controller, Get, Post, Put, Delete, Body, Param, Query, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DepartmentService } from './department.service';
import { Department } from './domain/department.entity';
import { CreateDepartmentDto, UpdateDepartmentDto, DepartmentQueryDto } from '@common/dto/department.dto';

@ApiTags('departments')
@Controller('departments')
export class DepartmentController {
  constructor(private readonly departmentService: DepartmentService) {}

  @Get()
  @ApiOperation({ summary: 'Get all departments with optional filtering' })
  @ApiResponse({ status: 200, description: 'Return all departments' })
  async findAll(@Query() query: DepartmentQueryDto) {
    const [departments, total] = await this.departmentService.findAll(query);
    return {
      data: departments,
      total
    };
  }

  @Get('tree')
  @ApiOperation({ summary: 'Get department tree' })
  @ApiResponse({ status: 200, description: 'Return department tree' })
  async getDepartmentTree() {
    return this.departmentService.getDepartmentTree();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get department by id' })
  @ApiResponse({ status: 200, description: 'Return department by id' })
  @ApiResponse({ status: 404, description: 'Department not found' })
  async findOne(@Param('id') id: number) {
    const department = await this.departmentService.findOne(id);
    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }
    return department;
  }

  @Post()
  @ApiOperation({ summary: 'Create a new department' })
  @ApiResponse({ status: 201, description: 'The department has been successfully created' })
  async create(@Body() createDepartmentDto: CreateDepartmentDto) {
    return this.departmentService.create(createDepartmentDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a department' })
  @ApiResponse({ status: 200, description: 'The department has been successfully updated' })
  @ApiResponse({ status: 404, description: 'Department not found' })
  async update(@Param('id') id: number, @Body() updateDepartmentDto: UpdateDepartmentDto) {
    const existingDepartment = await this.departmentService.findOne(id);
    if (!existingDepartment) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }
    return this.departmentService.update(id, updateDepartmentDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a department' })
  @ApiResponse({ status: 200, description: 'The department has been successfully deleted' })
  @ApiResponse({ status: 404, description: 'Department not found' })
  async remove(@Param('id') id: number) {
    const existingDepartment = await this.departmentService.findOne(id);
    if (!existingDepartment) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }
    await this.departmentService.remove(id);
    return { success: true };
  }
}
