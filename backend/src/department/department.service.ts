import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Department } from './domain/department.entity';
import { CreateDepartmentDto, UpdateDepartmentDto, DepartmentQueryDto } from '@common/dto/department.dto';

@Injectable()
export class DepartmentService {
  constructor(
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
  ) {}

  /**
   * Find all departments with optional filtering
   */
  async findAll(query?: DepartmentQueryDto): Promise<[Department[], number]> {
    const queryBuilder = this.departmentRepository.createQueryBuilder('department');

    if (query?.name) {
      queryBuilder.andWhere('department.name LIKE :name', { name: `%${query.name}%` });
    }

    if (query?.status !== undefined) {
      queryBuilder.andWhere('department.status = :status', { status: query.status });
    }

    if (query?.parentId !== undefined) {
      queryBuilder.andWhere('department.parent_id = :parentId', { parentId: query.parentId });
    }

    queryBuilder.orderBy('department.order_num', 'ASC');

    return queryBuilder.getManyAndCount();
  }

  /**
   * Find department by id
   */
  async findOne(id: number): Promise<Department> {
    return this.departmentRepository.findOne({
      where: { id },
      relations: ['children'],
    });
  }

  /**
   * Get department tree
   */
  async getDepartmentTree(): Promise<Department[]> {
    const allDepartments = await this.departmentRepository.find({
      order: {
        orderNum: 'ASC',
      },
    });

    // Filter root departments (parentId is null or 0)
    const rootDepartments = allDepartments.filter(dept => !dept.parentId);

    // Build tree recursively
    const buildTree = (departments: Department[], parentId: number = null) => {
      const result = [];
      for (const dept of departments) {
        if ((dept.parentId === parentId) || (!dept.parentId && !parentId)) {
          const children = buildTree(departments, dept.id);
          if (children.length) {
            dept.children = children;
          }
          result.push(dept);
        }
      }
      return result;
    };

    return buildTree(allDepartments);
  }

  /**
   * Create a new department
   */
  async create(createDepartmentDto: CreateDepartmentDto): Promise<Department> {
    const department = this.departmentRepository.create(createDepartmentDto);
    return this.departmentRepository.save(department);
  }

  /**
   * Update a department
   */
  async update(id: number, updateDepartmentDto: UpdateDepartmentDto): Promise<Department> {
    await this.departmentRepository.update(id, updateDepartmentDto);
    return this.departmentRepository.findOne({ where: { id } });
  }

  /**
   * Delete a department
   */
  async remove(id: number): Promise<void> {
    await this.departmentRepository.delete(id);
  }

  /**
   * Initialize default departments if none exist
   */
  async initializeDefaultDepartments(): Promise<void> {
    const count = await this.departmentRepository.count();
    if (count === 0) {
      // Create default departments
      const defaultDepartments = [
        { name: '总公司', orderNum: 1 },
        { name: '研发部', parentId: 1, orderNum: 1 },
        { name: '销售部', parentId: 1, orderNum: 2 },
        { name: '前端组', parentId: 2, orderNum: 1 },
        { name: '后端组', parentId: 2, orderNum: 2 },
        { name: '国内销售组', parentId: 3, orderNum: 1 },
        { name: '海外销售组', parentId: 3, orderNum: 2 },
      ];

      for (const dept of defaultDepartments) {
        await this.create(dept as CreateDepartmentDto);
      }
    }
  }
}
