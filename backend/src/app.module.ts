import { Mo<PERSON>le, NestModule, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { join, resolve } from 'path';
import { UserModule } from './user/user.module';
import { AuthModule } from './auth/auth.module';
import { SmsModule } from './sms/sms.module';
import { MenuModule } from './menu/menu.module';
import { DepartmentModule } from './department/department.module';
import { DictionaryModule } from './dictionary/dictionary.module';
import { RoleModule } from './role/role.module';
import { ResourceModule } from './resource/resource.module';
import { RbacModule } from './rbac/rbac.module';
import { NextModule } from './next/next.module';
import { NextMiddleware } from './next/next.middleware';
import { User } from './user/domain/user.entity';
import { UserAuth } from './auth/domain/user-auth.entity';
import { Menu } from './menu/domain/menu.entity';
import { Role } from './role/domain/role.entity';
import { Resource } from './resource/domain/resource.entity';
import { RoleMenuResource } from './rbac/domain/role-menu-resource.entity';
import { SnakeCaseNamingStrategy } from './database/snake-case-naming.strategy';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: resolve(__dirname, '../../.env') });

// Log environment variables for debugging
console.log('Environment variables:');
console.log(`DB_HOST: ${process.env.DB_HOST}`);
console.log(`DB_PORT: ${process.env.DB_PORT}`);
console.log(`DB_USERNAME: ${process.env.DB_USERNAME}`);
console.log(`DB_PASSWORD: ${process.env.DB_PASSWORD}`);
console.log(`DB_DATABASE: ${process.env.DB_DATABASE}`);

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: resolve(__dirname, '../../.env'),
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: '**************',
      port: 5432,
      username: 'demo_shop_user',
      password: 'Demo_shop@2024_Secure',
      database: 'demo_shop',
      entities: [User, UserAuth, Menu, Role, Resource, RoleMenuResource],
      synchronize: false,
      autoLoadEntities: true,
      ssl: false,
      retryAttempts: 10,
      retryDelay: 3000,
      logging: true,
      namingStrategy: new SnakeCaseNamingStrategy(),
    }),
    // Import RootModule first to ensure API routes are registered before NextModule
    UserModule,
    AuthModule,
    SmsModule,
    MenuModule,
    DepartmentModule,
    DictionaryModule,
    RoleModule,
    ResourceModule,
    RbacModule,
    NextModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply NextMiddleware to all routes except API routes
    // This ensures that API routes are handled by NestJS controllers
    consumer
      .apply(NextMiddleware)
      .exclude(
        { path: 'api/(.*)', method: RequestMethod.ALL } // Exclude all API routes
      )
      .forRoutes('*');
  }
}