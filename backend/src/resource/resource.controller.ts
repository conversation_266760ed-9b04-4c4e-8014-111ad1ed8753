import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ResourceService, CreateResourceDto, UpdateResourceDto, ResourceQueryDto } from './resource.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@ApiTags('resources')
@Controller('resources')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ResourceController {
  constructor(private readonly resourceService: ResourceService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new resource' })
  @ApiResponse({ status: 201, description: 'Resource created successfully' })
  @ApiResponse({ status: 409, description: 'Resource with this code already exists' })
  create(@Body() createResourceDto: CreateResourceDto) {
    return this.resourceService.create(createResourceDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all resources with pagination and filtering' })
  @ApiResponse({ status: 200, description: 'Resources retrieved successfully' })
  findAll(@Query() query: ResourceQueryDto) {
    return this.resourceService.findAll(query);
  }

  @Get('active')
  @ApiOperation({ summary: 'Get all active resources for selection' })
  @ApiResponse({ status: 200, description: 'Active resources retrieved successfully' })
  getActiveResources() {
    return this.resourceService.getActiveResources();
  }

  @Get('menu/:menuId')
  @ApiOperation({ summary: 'Get resources by menu ID' })
  @ApiResponse({ status: 200, description: 'Menu resources retrieved successfully' })
  getResourcesByMenu(@Param('menuId') menuId: string) {
    return this.resourceService.getResourcesByMenu(+menuId);
  }

  @Post('initialize')
  @ApiOperation({ summary: 'Initialize default resources for existing menus' })
  @ApiResponse({ status: 200, description: 'Default resources initialized successfully' })
  initializeDefaultResources() {
    return this.resourceService.initializeDefaultResources();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get resource by ID' })
  @ApiResponse({ status: 200, description: 'Resource retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Resource not found' })
  findOne(@Param('id') id: string) {
    return this.resourceService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update resource' })
  @ApiResponse({ status: 200, description: 'Resource updated successfully' })
  @ApiResponse({ status: 404, description: 'Resource not found' })
  @ApiResponse({ status: 409, description: 'Resource with this code already exists' })
  update(@Param('id') id: string, @Body() updateResourceDto: UpdateResourceDto) {
    return this.resourceService.update(+id, updateResourceDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete resource' })
  @ApiResponse({ status: 200, description: 'Resource deleted successfully' })
  @ApiResponse({ status: 404, description: 'Resource not found' })
  remove(@Param('id') id: string) {
    return this.resourceService.remove(+id);
  }
}
