import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Resource, ResourceType } from './domain/resource.entity';
import { Menu } from '../menu/domain/menu.entity';

export interface CreateResourceDto {
  code: string;
  name: string;
  description?: string;
  type: ResourceType;
  menuId?: number;
  apiPath?: string;
  apiMethod?: string;
  status?: boolean;
  orderNum?: number;
}

export interface UpdateResourceDto {
  code?: string;
  name?: string;
  description?: string;
  type?: ResourceType;
  menuId?: number;
  apiPath?: string;
  apiMethod?: string;
  status?: boolean;
  orderNum?: number;
}

export interface ResourceQueryDto {
  page?: number;
  limit?: number;
  name?: string;
  code?: string;
  type?: ResourceType;
  menuId?: number;
  status?: boolean;
}

@Injectable()
export class ResourceService {
  constructor(
    @InjectRepository(Resource)
    private resourceRepository: Repository<Resource>,
    @InjectRepository(Menu)
    private menuRepository: Repository<Menu>,
  ) {}

  /**
   * Create a new resource
   */
  async create(createResourceDto: CreateResourceDto): Promise<Resource> {
    // Check if resource with same code already exists
    const existingResource = await this.resourceRepository.findOne({
      where: { code: createResourceDto.code },
    });

    if (existingResource) {
      throw new ConflictException('Resource with this code already exists');
    }

    // Validate menu exists if menuId is provided
    if (createResourceDto.menuId) {
      const menu = await this.menuRepository.findOne({
        where: { id: createResourceDto.menuId },
      });
      if (!menu) {
        throw new NotFoundException('Menu not found');
      }
    }

    const resource = this.resourceRepository.create(createResourceDto);
    return this.resourceRepository.save(resource);
  }

  /**
   * Find all resources with pagination and filtering
   */
  async findAll(query: ResourceQueryDto): Promise<{ data: Resource[]; total: number }> {
    const { page = 1, limit = 10, name, code, type, menuId, status } = query;
    const queryBuilder = this.resourceRepository.createQueryBuilder('resource')
      .leftJoinAndSelect('resource.menu', 'menu');

    if (name) {
      queryBuilder.andWhere('resource.name ILIKE :name', { name: `%${name}%` });
    }

    if (code) {
      queryBuilder.andWhere('resource.code ILIKE :code', { code: `%${code}%` });
    }

    if (type) {
      queryBuilder.andWhere('resource.type = :type', { type });
    }

    if (menuId) {
      queryBuilder.andWhere('resource.menuId = :menuId', { menuId });
    }

    if (status !== undefined) {
      queryBuilder.andWhere('resource.status = :status', { status });
    }

    queryBuilder
      .orderBy('resource.orderNum', 'ASC')
      .addOrderBy('resource.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    return { data, total };
  }

  /**
   * Find resource by ID
   */
  async findOne(id: number): Promise<Resource> {
    const resource = await this.resourceRepository.findOne({
      where: { id },
      relations: ['menu'],
    });

    if (!resource) {
      throw new NotFoundException('Resource not found');
    }

    return resource;
  }

  /**
   * Update resource
   */
  async update(id: number, updateResourceDto: UpdateResourceDto): Promise<Resource> {
    const resource = await this.findOne(id);

    // Check if updating code conflicts with existing resources
    if (updateResourceDto.code) {
      const existingResource = await this.resourceRepository.findOne({
        where: { code: updateResourceDto.code },
      });

      if (existingResource && existingResource.id !== id) {
        throw new ConflictException('Resource with this code already exists');
      }
    }

    // Validate menu exists if menuId is provided
    if (updateResourceDto.menuId) {
      const menu = await this.menuRepository.findOne({
        where: { id: updateResourceDto.menuId },
      });
      if (!menu) {
        throw new NotFoundException('Menu not found');
      }
    }

    Object.assign(resource, updateResourceDto);
    return this.resourceRepository.save(resource);
  }

  /**
   * Delete resource
   */
  async remove(id: number): Promise<void> {
    const resource = await this.findOne(id);
    await this.resourceRepository.remove(resource);
  }

  /**
   * Get resources by menu ID
   */
  async getResourcesByMenu(menuId: number): Promise<Resource[]> {
    return this.resourceRepository.find({
      where: { menuId, status: true },
      order: { orderNum: 'ASC', name: 'ASC' },
    });
  }

  /**
   * Get all active resources for selection
   */
  async getActiveResources(): Promise<Resource[]> {
    return this.resourceRepository.find({
      where: { status: true },
      relations: ['menu'],
      order: { orderNum: 'ASC', name: 'ASC' },
    });
  }

  /**
   * Initialize default resources for existing menus
   */
  async initializeDefaultResources(): Promise<void> {
    const menus = await this.menuRepository.find();
    
    for (const menu of menus) {
      if (menu.resourceCode) {
        // Check if resource already exists
        const existingResource = await this.resourceRepository.findOne({
          where: { code: menu.resourceCode },
        });

        if (!existingResource) {
          // Create default resource for menu
          const resource = this.resourceRepository.create({
            code: menu.resourceCode,
            name: `${menu.name} - Menu Access`,
            description: `Access permission for ${menu.name} menu`,
            type: ResourceType.MENU,
            menuId: menu.id,
            status: true,
            orderNum: 0,
          });

          await this.resourceRepository.save(resource);

          // Create additional resources for common operations
          const operations = [
            { suffix: '_VIEW', name: 'View', description: 'View permission' },
            { suffix: '_CREATE', name: 'Create', description: 'Create permission' },
            { suffix: '_UPDATE', name: 'Update', description: 'Update permission' },
            { suffix: '_DELETE', name: 'Delete', description: 'Delete permission' },
          ];

          for (const [index, op] of operations.entries()) {
            const opResource = this.resourceRepository.create({
              code: `${menu.resourceCode}${op.suffix}`,
              name: `${menu.name} - ${op.name}`,
              description: `${op.description} for ${menu.name}`,
              type: ResourceType.BUTTON,
              menuId: menu.id,
              status: true,
              orderNum: index + 1,
            });

            await this.resourceRepository.save(opResource);
          }
        }
      }
    }
  }
}
