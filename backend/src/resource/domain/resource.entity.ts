import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { Menu } from '../../menu/domain/menu.entity';
import { RoleMenuResource } from '../../rbac/domain/role-menu-resource.entity';

export enum ResourceType {
  MENU = 'menu',
  BUTTON = 'button',
  API = 'api',
  PAGE = 'page'
}

@Entity('resources')
export class Resource {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  code: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ResourceType,
    default: ResourceType.MENU
  })
  type: ResourceType;

  @Column({ nullable: true })
  menuId: number;

  @ManyToOne(() => Menu, { nullable: true })
  @JoinColumn({ name: 'menu_id' })
  menu: Menu;

  @Column({ nullable: true })
  apiPath: string;

  @Column({ nullable: true })
  apiMethod: string;

  @Column({ default: true })
  status: boolean;

  @Column({ default: 0 })
  orderNum: number;

  @OneToMany(() => RoleMenuResource, roleMenuResource => roleMenuResource.resource)
  roleMenuResources: RoleMenuResource[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
