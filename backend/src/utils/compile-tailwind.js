/**
 * Tailwind CSS Compiler for NestJS Backend
 * 
 * This script compiles Tailwind CSS for the frontend when served through the NestJS backend.
 * It uses absolute paths and ensures the correct Tailwind CSS binary is used.
 */
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get absolute paths
const rootDir = path.resolve(__dirname, '../../..');
const frontendDir = path.join(rootDir, 'frontend');
const outputDir = path.join(frontendDir, 'public/css');

// Ensure the output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

console.log('Compiling TailwindCSS from NestJS backend...');
console.log(`Frontend directory: ${frontendDir}`);
console.log(`Output directory: ${outputDir}`);

// Function to compile Tailwind CSS
function compileTailwind() {
  try {
    // Use the npx from the frontend directory to ensure it finds the correct Tailwind CSS binary
    const npxPath = path.join(frontendDir, 'node_modules/.bin/npx');
    
    // Check if npx exists in the frontend's node_modules
    if (!fs.existsSync(npxPath)) {
      console.log('npx not found in frontend node_modules, using global npx');
      
      // Use the Tailwind CSS binary directly from the frontend's node_modules
      const tailwindPath = path.join(frontendDir, 'node_modules/.bin/tailwindcss');
      
      if (fs.existsSync(tailwindPath)) {
        console.log(`Using Tailwind CSS binary at: ${tailwindPath}`);
        
        // Execute Tailwind CSS directly with absolute paths
        execSync(
          `${tailwindPath} -i ${path.join(frontendDir, 'styles/globals.css')} -o ${path.join(outputDir, 'tailwind.css')} --minify`,
          { stdio: 'inherit', cwd: frontendDir }
        );
      } else {
        // Fallback to using npx with the full path to the frontend directory
        console.log('Tailwind CSS binary not found, using npx with full path');
        execSync(
          `npx tailwindcss -i ${path.join(frontendDir, 'styles/globals.css')} -o ${path.join(outputDir, 'tailwind.css')} --minify`,
          { stdio: 'inherit', cwd: frontendDir }
        );
      }
    } else {
      // Use the npx from the frontend directory
      console.log(`Using npx at: ${npxPath}`);
      execSync(
        `${npxPath} tailwindcss -i ${path.join(frontendDir, 'styles/globals.css')} -o ${path.join(outputDir, 'tailwind.css')} --minify`,
        { stdio: 'inherit', cwd: frontendDir }
      );
    }
    
    console.log('TailwindCSS compilation completed successfully.');
    return true;
  } catch (error) {
    console.error('Error compiling TailwindCSS:', error.message);
    return false;
  }
}

// Export the function for use in the NestJS application
module.exports = {
  compileTailwind
};

// If this script is run directly, compile Tailwind CSS
if (require.main === module) {
  const success = compileTailwind();
  process.exit(success ? 0 : 1);
}
