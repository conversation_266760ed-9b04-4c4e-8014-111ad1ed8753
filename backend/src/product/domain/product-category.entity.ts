import { <PERSON><PERSON>ty, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';

@Entity('product_categories')
export class ProductCategory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  image: string;

  @Column({ default: 0 })
  orderNum: number;

  @Column({ nullable: true })
  parentId: number;

  @ManyToOne(() => ProductCategory, category => category.children)
  @JoinColumn({ name: 'parent_id' })
  parent: ProductCategory;

  @OneToMany(() => ProductCategory, category => category.parent)
  children: ProductCategory[];
  // Note: This will be added after Product entity is imported
  // @OneToMany(() => Product, product => product.category)
  // products: Product[];

  @Column({ default: true })
  status: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
