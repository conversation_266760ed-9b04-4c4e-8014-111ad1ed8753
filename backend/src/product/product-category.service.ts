import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, FindOptionsWhere, IsNull } from 'typeorm';
import { ProductCategory } from './domain/product-category.entity';
import { CreateProductCategoryDto, UpdateProductCategoryDto, ProductCategoryQueryDto, ProductCategoryTreeDto } from '@common/dto/product-category.dto';

@Injectable()
export class ProductCategoryService {
  constructor(
    @InjectRepository(ProductCategory)
    private productCategoryRepository: Repository<ProductCategory>,
  ) {}

  /**
   * Create a new product category
   */
  async create(createProductCategoryDto: CreateProductCategoryDto): Promise<ProductCategory> {
    // Check if category with same name already exists at the same level
    const existingCategory = await this.productCategoryRepository.findOne({
      where: {
        name: createProductCategoryDto.name,
        parentId: createProductCategoryDto.parentId || IsNull(),
      },
    });

    if (existingCategory) {
      throw new ConflictException('Category with this name already exists at this level');
    }

    // If parentId is provided, verify parent exists
    if (createProductCategoryDto.parentId) {
      const parent = await this.productCategoryRepository.findOne({
        where: { id: createProductCategoryDto.parentId },
      });
      if (!parent) {
        throw new NotFoundException('Parent category not found');
      }
    }

    const category = this.productCategoryRepository.create(createProductCategoryDto);
    return this.productCategoryRepository.save(category);
  }

  /**
   * Get all categories with pagination and filtering
   */
  async findAll(queryDto: ProductCategoryQueryDto) {
    const {
      name,
      status,
      parentId,
      sortBy = 'orderNum',
      sortOrder = 'ASC',
      page = 1,
      limit = 10
    } = queryDto;

    const where: FindOptionsWhere<ProductCategory> = {};

    if (name) {
      where.name = Like(`%${name}%`);
    }

    if (status !== undefined) {
      where.status = status;
    }

    if (parentId !== undefined) {
      where.parentId = parentId === 0 ? IsNull() : parentId;
    }

    const [categories, total] = await this.productCategoryRepository.findAndCount({
      where,
      relations: ['parent', 'children'],
      order: {
        [sortBy]: sortOrder,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: categories,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get category tree structure
   */
  async getTree(): Promise<ProductCategoryTreeDto[]> {
    const categories = await this.productCategoryRepository.find({
      relations: ['children'],
      order: { orderNum: 'ASC' },
    });

    // Build tree structure
    const categoryMap = new Map<number, ProductCategoryTreeDto>();
    const rootCategories: ProductCategoryTreeDto[] = [];

    // First pass: create all category objects
    categories.forEach(category => {
      const categoryDto: ProductCategoryTreeDto = {
        id: category.id,
        name: category.name,
        description: category.description,
        image: category.image,
        orderNum: category.orderNum,
        parentId: category.parentId,
        status: category.status,
        children: [],
        productCount: 0, // Will be calculated if needed
        createdAt: category.createdAt,
        updatedAt: category.updatedAt,
      };
      categoryMap.set(category.id, categoryDto);
    });

    // Second pass: build tree structure
    categories.forEach(category => {
      const categoryDto = categoryMap.get(category.id)!;
      if (category.parentId) {
        const parent = categoryMap.get(category.parentId);
        if (parent) {
          parent.children!.push(categoryDto);
        }
      } else {
        rootCategories.push(categoryDto);
      }
    });

    return rootCategories;
  }

  /**
   * Get a single category by ID
   */
  async findOne(id: number): Promise<ProductCategory> {
    const category = await this.productCategoryRepository.findOne({
      where: { id },
      relations: ['parent', 'children'],
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    return category;
  }

  /**
   * Update a category
   */
  async update(id: number, updateProductCategoryDto: UpdateProductCategoryDto): Promise<ProductCategory> {
    const category = await this.findOne(id);

    // Check if name conflict exists (excluding current category)
    if (updateProductCategoryDto.name) {
      const existingCategory = await this.productCategoryRepository.findOne({
        where: {
          name: updateProductCategoryDto.name,
          parentId: updateProductCategoryDto.parentId || category.parentId || IsNull(),
          id: Not(id),
        },
      });

      if (existingCategory) {
        throw new ConflictException('Category with this name already exists at this level');
      }
    }

    // If parentId is being changed, verify new parent exists and prevent circular reference
    if (updateProductCategoryDto.parentId !== undefined && updateProductCategoryDto.parentId !== category.parentId) {
      if (updateProductCategoryDto.parentId) {
        const parent = await this.productCategoryRepository.findOne({
          where: { id: updateProductCategoryDto.parentId },
        });
        if (!parent) {
          throw new NotFoundException('Parent category not found');
        }

        // Check for circular reference
        if (await this.wouldCreateCircularReference(id, updateProductCategoryDto.parentId)) {
          throw new ConflictException('Cannot set parent: would create circular reference');
        }
      }
    }

    await this.productCategoryRepository.update(id, updateProductCategoryDto);
    return this.findOne(id);
  }

  /**
   * Delete a category
   */
  async remove(id: number): Promise<void> {
    const category = await this.findOne(id);

    // Check if category has children
    const childrenCount = await this.productCategoryRepository.count({
      where: { parentId: id },
    });

    if (childrenCount > 0) {
      throw new ConflictException('Cannot delete category with subcategories');
    }

    // TODO: Check if category has products when Product entity is available
    // const productsCount = await this.productRepository.count({
    //   where: { categoryId: id },
    // });
    // if (productsCount > 0) {
    //   throw new ConflictException('Cannot delete category with products');
    // }

    await this.productCategoryRepository.delete(id);
  }

  /**
   * Check if setting a parent would create a circular reference
   */
  private async wouldCreateCircularReference(categoryId: number, parentId: number): Promise<boolean> {
    let currentParentId = parentId;
    
    while (currentParentId) {
      if (currentParentId === categoryId) {
        return true;
      }
      
      const parent = await this.productCategoryRepository.findOne({
        where: { id: currentParentId },
        select: ['parentId'],
      });
      
      currentParentId = parent?.parentId || null;
    }
    
    return false;
  }
}
