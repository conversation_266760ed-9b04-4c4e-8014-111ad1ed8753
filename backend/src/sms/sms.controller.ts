import { Controller, Post, Body, BadRequestException, Get } from '@nestjs/common';
import { SmsService } from './sms.service';
import { CaptchaService } from './captcha.service';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiProperty } from '@nestjs/swagger';
import { CaptchaResponseDto, ResponseDto, SmsResponseDto } from '../dto/response.dto';

export class SendSmsDto {
  @ApiProperty({ description: '手机号', example: '13800138000' })
  phone: string;

  @ApiProperty({ description: '图形验证码token', example: 'a1b2c3d4' })
  captchaToken: string;

  @ApiProperty({ description: '图形验证码文本', example: 'a1b2' })
  captchaText: string;
}

export class VerifySmsDto {
  @ApiProperty({ description: '手机号', example: '13800138000' })
  phone: string;

  @ApiProperty({ description: '短信验证码', example: '123456' })
  code: string;
}

@ApiTags('sms')
@Controller('sms')
export class SmsController {
  constructor(
    private readonly smsService: SmsService,
    private readonly captchaService: CaptchaService,
  ) {}

  @Get('captcha')
  @ApiOperation({ summary: '获取图形验证码' })
  @ApiResponse({ status: 200, description: '验证码生成成功', type: ResponseDto<CaptchaResponseDto> })
  async getCaptcha(): Promise<ResponseDto<CaptchaResponseDto>> {
    const captcha = this.captchaService.generateCaptcha();

    // 返回验证码图片和token
    return {
      success: true,
      data: {
        token: captcha.token,
        svg: captcha.svg,
      },
    };
  }

  @Post('send')
  @ApiOperation({ summary: '发送短信验证码' })
  @ApiResponse({ status: 200, description: '验证码发送成功', type: SmsResponseDto })
  @ApiResponse({ status: 400, description: '验证码发送失败' })
  @ApiBody({ type: SendSmsDto })
  async sendSmsCode(@Body() body: SendSmsDto): Promise<SmsResponseDto> {
    const { phone, captchaToken, captchaText } = body;

    // 调用短信服务发送验证码
    return this.smsService.sendSmsVerificationCode(phone, captchaToken, captchaText);
  }

  @Post('verify')
  @ApiOperation({ summary: '验证短信验证码' })
  @ApiResponse({ status: 200, description: '验证码验证成功', type: SmsResponseDto })
  @ApiResponse({ status: 400, description: '验证码验证失败' })
  @ApiBody({ type: VerifySmsDto })
  async verifySmsCode(@Body() body: VerifySmsDto): Promise<SmsResponseDto> {
    const { phone, code } = body;

    const isValid = await this.smsService.verifySmsCode(phone, code);

    return { success: isValid, message: '验证成功' };
  }
}
