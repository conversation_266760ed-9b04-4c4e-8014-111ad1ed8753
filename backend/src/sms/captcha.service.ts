import { Injectable } from '@nestjs/common';
import * as svgCaptcha from 'svg-captcha';
import * as crypto from 'crypto';

interface CaptchaData {
  token: string;
  text: string;
  timestamp: number;
}

@Injectable()
export class CaptchaService {
  private captchaStore: Map<string, CaptchaData> = new Map();
  private readonly CAPTCHA_EXPIRY = 5 * 60 * 1000; // 5分钟过期

  /**
   * 生成滑动验证码
   * @returns 验证码数据，包含token和svg
   */
  generateCaptcha(): { token: string; svg: string } {
    // 创建验证码
    const captcha = svgCaptcha.create({
      size: 4, // 验证码长度
      ignoreChars: '0o1il', // 排除容易混淆的字符
      noise: 2, // 干扰线条数量
      color: true, // 验证码的字符是否有颜色
      background: '#f0f0f0', // 背景颜色
      width: 150, // 图片宽度
      height: 40, // 图片高度
    });

    // 生成唯一token
    const token = crypto.randomBytes(32).toString('hex');

    // 存储验证码数据
    this.captchaStore.set(token, {
      token,
      text: captcha.text,
      timestamp: Date.now(),
    });

    // 清理过期的验证码
    this.cleanupExpiredCaptchas();

    return {
      token,
      svg: captcha.data, // SVG格式的验证码图片
    };
  }

  /**
   * 验证验证码
   * @param token 验证码token
   * @param text 用户输入的验证码文本
   * @returns 验证结果
   */
  verifyCaptcha(token: string, text: string): boolean {
    if (!token || !text) {
      return false;
    }

    const captchaData = this.captchaStore.get(token);
    if (!captchaData) {
      return false;
    }

    // 检查是否过期
    if (Date.now() - captchaData.timestamp > this.CAPTCHA_EXPIRY) {
      this.captchaStore.delete(token);
      return false;
    }

    // 验证成功后删除验证码数据
    const isValid = captchaData.text.toLowerCase() === text.toLowerCase();
    if (isValid) {
      this.captchaStore.delete(token);
    }

    return isValid;
  }

  /**
   * 清理过期的验证码
   */
  private cleanupExpiredCaptchas() {
    const now = Date.now();
    for (const [token, data] of this.captchaStore.entries()) {
      if (now - data.timestamp > this.CAPTCHA_EXPIRY) {
        this.captchaStore.delete(token);
      }
    }
  }
}
