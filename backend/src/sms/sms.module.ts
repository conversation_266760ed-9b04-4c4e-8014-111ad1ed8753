import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserAuth } from '../auth/domain/user-auth.entity';
import { SmsService } from './sms.service';
import { SmsController } from './sms.controller';
import { CaptchaService } from './captcha.service';

@Module({
  imports: [TypeOrmModule.forFeature([UserAuth])],
  controllers: [SmsController],
  providers: [SmsService, CaptchaService],
  exports: [SmsService, CaptchaService],
})
export class SmsModule {}
