import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {MoreThanOrEqual, Repository} from 'typeorm';
import { UserAuth } from '../auth/domain/user-auth.entity';
import * as crypto from 'crypto';
import Dysmsapi20170525, * as $Dysmsapi20170525 from '@alicloud/dysmsapi20170525';
import OpenApi, * as $OpenApi from '@alicloud/openapi-client';
import Util, * as $Util from '@alicloud/tea-util';
import Credential from '@alicloud/credentials';
import { ConfigService } from '@nestjs/config';
import { CaptchaService } from './captcha.service';
import {AuthType} from "@common/enums/auth-type.enum";

@Injectable()
export class SmsService {
  private client: Dysmsapi20170525;

  constructor(
    @InjectRepository(UserAuth)
    private userAuthRepository: Repository<UserAuth>,
    private configService: ConfigService,
    private captchaService: CaptchaService,
  ) {
    // 初始化阿里云短信客户端
    this.client = this.createClient();
  }

  /**
   * 创建阿里云短信客户端
   */
  private createClient(): Dysmsapi20170525 {
    const accessKeyId = this.configService.get('ALIYUN_ACCESS_KEY_ID');
    const accessKeySecret = this.configService.get('ALIYUN_ACCESS_KEY_SECRET');

    if (!accessKeyId || !accessKeySecret) {
      console.warn('阿里云短信服务配置缺失，将使用模拟模式');
      // 使用默认凭证
      let credential = new Credential();
      let config = new $OpenApi.Config({
        credential: credential,
      });
      config.endpoint = 'dysmsapi.aliyuncs.com';
      return new Dysmsapi20170525(config);
    }

    // 使用配置的凭证
    let config = new $OpenApi.Config({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
    });
    config.endpoint = 'dysmsapi.aliyuncs.com';
    return new Dysmsapi20170525(config);
  }

  /**
   * 生成6位数字验证码
   */
  generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * 检查全局短信发送频率限制
   * 每10分钟最多发送10条短信
   */
  async checkGlobalSmsLimit(): Promise<void> {
    const now = new Date();
    const tenMinutesAgo = new Date(now.getTime() - 10 * 60 * 1000);

    // 查询最近10分钟内发送的短信数量
    const recentSmsCount = await this.userAuthRepository.count({
      where: {
        authType: AuthType.PHONE,
        updatedAt: MoreThanOrEqual(tenMinutesAgo),
      }
    });

    if (recentSmsCount >= 10) {
      throw new BadRequestException('短信发送频率过高，请稍后再试');
    }
  }

  /**
   * 检查单个用户短信发送频率限制
   * 同一手机号每天最多发送3条短信
   */
  async checkUserSmsLimit(phone: string): Promise<void> {
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // 查询该手机号今天发送的短信数量
    const userSmsCount = await this.userAuthRepository.count({
      where: {
        account: phone,
        authType: AuthType.PHONE,
        updatedAt: MoreThanOrEqual(todayStart),
      }
    });

    if (userSmsCount >= 3) {
      throw new BadRequestException('您今天的短信发送次数已达上限，请明天再试');
    }
  }

  /**
   * 发送短信验证码
   * @param phone 手机号
   * @param captchaToken 验证码token
   * @param captchaText 验证码文本
   * @returns 发送结果
   */
  async sendSmsVerificationCode(phone: string, captchaToken: string, captchaText: string): Promise<{ success: boolean; message: string }> {
    try {
      // 验证手机号格式
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        throw new BadRequestException('手机号格式不正确');
      }

      // 验证图形验证码
      const isCaptchaValid = this.captchaService.verifyCaptcha(captchaToken, captchaText);
      if (!isCaptchaValid) {
        throw new BadRequestException('图形验证码验证失败');
      }

      // 检查全局短信发送频率限制
      await this.checkGlobalSmsLimit();

      // 检查单个用户短信发送频率限制
      await this.checkUserSmsLimit(phone);

      // 检查是否有未过期的验证码
      const existingAuth = await this.userAuthRepository.findOne({
        where: {
          account: phone,
          authType: AuthType.PHONE,
        },
      });

      const now = new Date();
      if (existingAuth && existingAuth.expire && existingAuth.expire > now) {
        // 计算剩余秒数
        const remainingSeconds = Math.floor((existingAuth.expire.getTime() - now.getTime()) / 1000);
        throw new BadRequestException(`验证码已发送，${remainingSeconds}秒后可重新获取`);
      }

      // 生成验证码
      const code = this.generateVerificationCode();

      // 设置过期时间为3分钟后
      const expire = new Date();
      expire.setMinutes(expire.getMinutes() + 3);

      // 准备发送短信
      const signName = this.configService.get('ALIYUN_SMS_SIGN_NAME') || '阿里云短信测试';
      const templateCode = this.configService.get('ALIYUN_SMS_TEMPLATE_CODE') || 'SMS_154950909';

      const sendSmsRequest = new $Dysmsapi20170525.SendSmsRequest({
        signName: signName,
        templateCode: templateCode,
        phoneNumbers: phone,
        templateParam: JSON.stringify({ code }),
      });

      const runtime = new $Util.RuntimeOptions({});

      // 判断是否为开发环境
      const isDev = this.configService.get('NODE_ENV') !== 'production';

      let result;
      if (isDev) {
        // 开发环境下模拟发送
        console.log(`[模拟] 向 ${phone} 发送验证码: ${code}`);
        result = { body: { Code: 'OK' } };
      } else {
        // 生产环境下实际发送
        try {
          result = await this.client.sendSmsWithOptions(sendSmsRequest, runtime);
        } catch (error) {
          console.error('发送短信失败:', error);
          throw new BadRequestException(`发送短信失败: ${error.message}`);
        }
      }

      if (result.body.Code === 'OK') {
        // 保存验证码到数据库
        let userAuth = existingAuth;
        if (!userAuth) {
          userAuth = new UserAuth();
          userAuth.account = phone;
          userAuth.authType = AuthType.PHONE;
        }

        userAuth.credential = code;
        userAuth.expire = expire;
        await this.userAuthRepository.save(userAuth);

        return { success: true, message: '验证码发送成功' };
      } else {
        throw new BadRequestException(`发送短信失败: ${result.body.Message}`);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      console.error('发送短信验证码失败:', error);
      throw new BadRequestException('验证码发送失败，请稍后重试');
    }
  }

  /**
   * 验证短信验证码
   * @param phone 手机号
   * @param code 验证码
   * @returns 验证结果
   */
  async verifySmsCode(phone: string, code: string): Promise<boolean> {
    try {
      // 查找验证码记录
      const userAuth = await this.userAuthRepository.findOne({
        where: {
          account: phone,
          authType: AuthType.PHONE,
        },
      });

      if (!userAuth) {
        throw new BadRequestException('验证码不存在');
      }

      // 检查验证码是否过期
      const now = new Date();
      if (!userAuth.expire || userAuth.expire < now) {
        throw new BadRequestException('验证码已过期');
      }

      // 检查验证码是否正确
      if (userAuth.credential !== code) {
        throw new BadRequestException('验证码错误');
      }

      // 验证成功后，将过期时间设置为当前时间，使其立即过期
      userAuth.expire = now;
      await this.userAuthRepository.save(userAuth);

      return true;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      console.error('验证短信验证码失败:', error);
      throw new BadRequestException('验证失败，请稍后重试');
    }
  }
}
