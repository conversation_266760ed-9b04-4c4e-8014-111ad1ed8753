import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../user/domain/user.entity';
import { UserAuth } from './domain/user-auth.entity';
import { LoginDto, RefreshTokenDto } from '../dto/auth.dto';
import { AuthResponse, JwtPayload } from '@common/interfaces/auth.interface';
import { UserStatus } from '@common/enums/user-status.enum';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserAuth)
    private userAuthRepository: Repository<UserAuth>,
    private jwtService: JwtService,
  ) {}

  async login(loginDto: LoginDto): Promise<AuthResponse> {
    const { account, authCredential, authType, rememberMe = false } = loginDto;

    // 查找用户认证记录
    const userAuth = await this.userAuthRepository.findOne({
      where: { account, authType },
      relations: ['user'],
    });

    // 如果找不到用户认证记录，返回错误
    if (!userAuth) {
      throw new UnauthorizedException('User authentication record not found');
    }

    // 验证凭证
    const isValidCredential = await this.validateCredential(
      authType,
      authCredential,
      userAuth.credential,
    );

    if (!isValidCredential) {
      throw new UnauthorizedException('Invalid credentials');
    }

    let user = userAuth.user;
    let isNewUser = false;

    // 如果找到认证记录但没有找到用户，创建新用户
    if (!user) {
      isNewUser = true;

      // 创建新用户
      user = new User();
      user.username = account;
      user.email = account.includes('@') ? account : null;
      user.phone = account.match(/^\d{11}$/) ? account : null;
      user.avatar = 'https://api.dicebear.com/7.x/avataaars/svg?seed=' + account; // 默认头像
      user.status = UserStatus.ACTIVE;

      await this.userRepository.save(user);

      // 更新用户认证记录的 user 关系
      userAuth.user = user;
      await this.userAuthRepository.save(userAuth);
    }

    // 重新加载用户以获取角色信息
    user = await this.userRepository.findOne({
      where: { id: user.id },
      relations: ['role'],
    });

    // 生成 JWT token
    const payload: JwtPayload = {
      sub: user.id.toString(),
      username: user.username,
      authType,
      roleId: user.role?.id,
      roleCode: user.role?.code,
    };

    // 根据是否记住我设置不同的过期时间
    const accessTokenExpiresIn = rememberMe ? '24h' : '1h';
    const refreshTokenExpiresIn = rememberMe ? '30d' : '7d';

    const accessToken = this.jwtService.sign(payload, { expiresIn: accessTokenExpiresIn });
    const refreshToken = this.jwtService.sign(payload, { expiresIn: refreshTokenExpiresIn });

    // 更新用户的 token 和 refreshToken
    user.token = accessToken;

    // 如果选择了记住我，则存储 refreshToken 到数据库
    if (rememberMe) {
      user.refreshToken = refreshToken;
      // 设置 refreshToken 过期时间
      const refreshTokenExpiry = new Date();
      refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30); // 30天后过期
      user.refreshTokenExpiresAt = refreshTokenExpiry;
    } else {
      user.refreshToken = null;
      user.refreshTokenExpiresAt = null;
    }

    await this.userRepository.save(user);

    return {
      accessToken,
      refreshToken,
      user,
      isNewUser,
    };
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    let payload: JwtPayload;

    try {
      // 验证 refreshToken
      payload = this.jwtService.verify(refreshToken) as JwtPayload;
    } catch (error) {
      // JWT 验证错误
      throw new UnauthorizedException('Invalid refresh token');
    }

    // 查找用户
    const user = await this.userRepository.findOne({
      where: { id: parseInt(payload.sub) },
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // 检查数据库中的 refreshToken 是否匹配（如果用户选择了记住我）
    if (user.refreshToken) {
      // 检查 refreshToken 是否匹配
      if (user.refreshToken !== refreshToken) {
        throw new UnauthorizedException('Refresh token does not match');
      }

      // 检查 refreshToken 是否过期
      if (user.refreshTokenExpiresAt && new Date() > user.refreshTokenExpiresAt) {
        // 清除过期的 refreshToken
        user.refreshToken = null;
        user.refreshTokenExpiresAt = null;
        await this.userRepository.save(user);
        throw new UnauthorizedException('Refresh token has expired');
      }
    }

    const newPayload: JwtPayload = {
      sub: user.id.toString(),
      username: user.username,
      authType: payload.authType,
    };

    // 判断是否是记住我模式
    const isRememberMe = !!user.refreshToken;

    // 根据是否记住我设置不同的过期时间
    const accessTokenExpiresIn = isRememberMe ? '24h' : '1h';
    const refreshTokenExpiresIn = isRememberMe ? '30d' : '7d';

    const accessToken = this.jwtService.sign(newPayload, { expiresIn: accessTokenExpiresIn });
    const newRefreshToken = this.jwtService.sign(newPayload, { expiresIn: refreshTokenExpiresIn });

    // 更新用户的 token 和 refreshToken
    user.token = accessToken;

    // 如果是记住我模式，更新数据库中的 refreshToken
    if (isRememberMe) {
      user.refreshToken = newRefreshToken;
      // 更新 refreshToken 过期时间
      const refreshTokenExpiry = new Date();
      refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30); // 30天后过期
      user.refreshTokenExpiresAt = refreshTokenExpiry;
    }

    await this.userRepository.save(user);

    return {
      accessToken,
      refreshToken: newRefreshToken,
      user,
      isNewUser: false,
    };
  }

  private async validateCredential(
    authType: string,
    plainCredential: string,
    hashedCredential: string,
  ): Promise<boolean> {
    if (authType === 'password') {
      return bcrypt.compare(plainCredential, hashedCredential);
    }

    // 对于第三方登录，直接比较凭证
    return plainCredential === hashedCredential;
  }

  private async hashCredential(
    authType: string,
    credential: string,
  ): Promise<string> {
    if (authType === 'password') {
      const salt = await bcrypt.genSalt();
      return bcrypt.hash(credential, salt);
    }

    // 对于第三方登录，不需要哈希
    return credential;
  }
}
