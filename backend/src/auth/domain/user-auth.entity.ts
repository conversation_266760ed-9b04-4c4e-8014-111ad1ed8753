import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, PrimaryGeneratedC<PERSON>umn, ManyToOne, <PERSON>in<PERSON><PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../user/domain/user.entity';
import { IUserAuth } from '@common/interfaces/user-auth.interface';
import { AuthType } from '@common/enums/auth-type.enum';

@Entity('user_auths')
export class UserAuth implements IUserAuth {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => User, user => user.authMethods, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({
    type: 'enum',
    enum: AuthType,
  })
  authType: AuthType;

  @Column({ nullable: true })
  account: string;

  @Column({ nullable: true })
  credential: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  expire: Date;
}
