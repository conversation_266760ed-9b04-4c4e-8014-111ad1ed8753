import { ApiProperty } from '@nestjs/swagger';

/**
 * 通用响应 DTO
 */
export class ResponseDto<T> {
  @ApiProperty({ description: '操作是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '操作成功', required: false })
  message?: string;

  @ApiProperty({ description: '响应数据', required: false })
  data?: T;
}

/**
 * 验证码响应 DTO
 */
export class CaptchaResponseDto {
  @ApiProperty({ description: '验证码 token', example: 'a1b2c3d4' })
  token: string;

  @ApiProperty({ description: '验证码 SVG 图片', example: '<svg>...</svg>' })
  svg: string;
}

/**
 * 短信验证码响应 DTO
 */
export class SmsResponseDto {
  @ApiProperty({ description: '操作是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '验证码发送成功' })
  message: string;
}
