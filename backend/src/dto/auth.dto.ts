import { IsEmail, IsEnum, Is<PERSON><PERSON>al, IsString, MinLength, IsBoolean } from 'class-validator';
import { ILoginDto, IRefreshTokenDto } from '@common/dto/auth.dto';
import { AuthType } from '@common/enums/auth-type.enum';

export class LoginDto implements ILoginDto {
  @IsString()
  account: string;

  @IsString()
  @MinLength(6)
  authCredential: string;

  @IsEnum(AuthType)
  authType: AuthType;

  @IsOptional()
  @IsBoolean()
  rememberMe?: boolean;
}

export class RefreshTokenDto implements IRefreshTokenDto {
  @IsString()
  refreshToken: string;

  @IsString()
  token: string;
}
