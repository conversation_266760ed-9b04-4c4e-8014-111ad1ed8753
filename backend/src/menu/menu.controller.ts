import { Controller, Get, Post, Put, Delete, Body, Param, Query, NotFoundException, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MenuService } from './menu.service';
import { Menu } from './domain/menu.entity';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@ApiTags('menus')
@Controller('menus')
export class MenuController {
  constructor(private readonly menuService: MenuService) {}

  @Get()
  @ApiOperation({ summary: 'Get all menus' })
  @ApiResponse({ status: 200, description: 'Return all menus' })
  async findAll() {
    return this.menuService.findAll();
  }

  @Get('tree')
  @ApiOperation({ summary: 'Get menu tree' })
  @ApiResponse({ status: 200, description: 'Return menu tree' })
  async getMenuTree() {
    return this.menuService.getMenuTree();
  }

  @Get('user-tree')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user menu tree with permission filtering' })
  @ApiResponse({ status: 200, description: 'Return user menu tree' })
  async getUserMenuTree(@Request() req) {
    return this.menuService.getUserMenuTree(req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get menu by id' })
  @ApiResponse({ status: 200, description: 'Return menu by id' })
  @ApiResponse({ status: 404, description: 'Menu not found' })
  async findOne(@Param('id') id: number) {
    const menu = await this.menuService.findOne(id);
    if (!menu) {
      throw new NotFoundException(`Menu with ID ${id} not found`);
    }
    return menu;
  }

  @Post()
  @ApiOperation({ summary: 'Create a new menu' })
  @ApiResponse({ status: 201, description: 'The menu has been successfully created' })
  async create(@Body() menu: Partial<Menu>) {
    return this.menuService.create(menu);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a menu' })
  @ApiResponse({ status: 200, description: 'The menu has been successfully updated' })
  @ApiResponse({ status: 404, description: 'Menu not found' })
  async update(@Param('id') id: number, @Body() menu: Partial<Menu>) {
    const existingMenu = await this.menuService.findOne(id);
    if (!existingMenu) {
      throw new NotFoundException(`Menu with ID ${id} not found`);
    }
    return this.menuService.update(id, menu);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a menu' })
  @ApiResponse({ status: 200, description: 'The menu has been successfully deleted' })
  @ApiResponse({ status: 404, description: 'Menu not found' })
  async remove(@Param('id') id: number) {
    const existingMenu = await this.menuService.findOne(id);
    if (!existingMenu) {
      throw new NotFoundException(`Menu with ID ${id} not found`);
    }
    await this.menuService.remove(id);
    return { success: true };
  }
}
