import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Menu } from './domain/menu.entity';
import { User } from '../user/domain/user.entity';
import { RoleMenuResource } from '../rbac/domain/role-menu-resource.entity';

@Injectable()
export class MenuService {
  constructor(
    @InjectRepository(Menu)
    private menuRepository: Repository<Menu>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(RoleMenuResource)
    private roleMenuResourceRepository: Repository<RoleMenuResource>,
  ) {}

  /**
   * Find all menus
   */
  async findAll(): Promise<Menu[]> {
    return this.menuRepository.find({
      order: {
        orderNum: 'ASC',
      },
    });
  }

  /**
   * Find menu by id
   */
  async findOne(id: number): Promise<Menu> {
    return this.menuRepository.findOne({
      where: { id },
      relations: ['children'],
    });
  }

  /**
   * Get menu tree
   */
  async getMenuTree(): Promise<Menu[]> {
    const allMenus = await this.menuRepository.find({
      order: {
        orderNum: 'ASC',
      },
    });

    // Filter root menus (parentId is null or 0)
    const rootMenus = allMenus.filter(menu => !menu.parentId);

    // Build tree recursively
    const buildTree = (menus: Menu[], parentId: number = null) => {
      const result = [];
      for (const menu of menus) {
        if ((menu.parentId === parentId) || (!menu.parentId && !parentId)) {
          const children = buildTree(menus, menu.id);
          if (children.length) {
            menu.children = children;
          }
          result.push(menu);
        }
      }
      return result;
    };

    return buildTree(allMenus);
  }

  /**
   * Get menu tree filtered by user permissions
   */
  async getUserMenuTree(userId: number): Promise<Menu[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role'],
    });

    if (!user || !user.role) {
      return [];
    }

    // Get all permissions for the user's role
    const permissions = await this.roleMenuResourceRepository.find({
      where: { roleId: user.role.id, granted: true },
      relations: ['menu', 'resource'],
    });

    // Extract unique menu IDs from resources
    const menuIds = [...new Set(permissions.map(p => p.resource?.menuId).filter(id => id))];

    if (menuIds.length === 0) {
      return [];
    }

    // Get accessible menus
    const accessibleMenus = await this.menuRepository.find({
      where: { id: In(menuIds), status: true },
      order: { orderNum: 'ASC' },
    });

    // Get all menus to build proper tree structure
    const allMenus = await this.menuRepository.find({
      where: { status: true },
      order: { orderNum: 'ASC' },
    });

    const accessibleMenuIds = new Set(accessibleMenus.map(m => m.id));

    // Build tree structure with permission filtering
    const buildTree = (menus: Menu[], parentId: number = null): Menu[] => {
      const result = [];
      for (const menu of menus) {
        if ((menu.parentId === parentId) || (!menu.parentId && !parentId)) {
          // Check if this menu is accessible or if it has accessible children
          const children = buildTree(menus, menu.id);
          const hasAccessibleChildren = children.length > 0;
          const isDirectlyAccessible = accessibleMenuIds.has(menu.id);

          // Include menu if it's directly accessible or has accessible children
          if (isDirectlyAccessible || hasAccessibleChildren) {
            menu.children = children;
            result.push(menu);
          }
        }
      }
      return result;
    };

    return buildTree(allMenus);
  }

  /**
   * Create a new menu
   */
  async create(menu: Partial<Menu>): Promise<Menu> {
    const newMenu = this.menuRepository.create(menu);
    return this.menuRepository.save(newMenu);
  }

  /**
   * Update a menu
   */
  async update(id: number, menu: Partial<Menu>): Promise<Menu> {
    await this.menuRepository.update(id, menu);
    return this.menuRepository.findOne({ where: { id } });
  }

  /**
   * Delete a menu
   */
  async remove(id: number): Promise<void> {
    await this.menuRepository.delete(id);
  }

  /**
   * Initialize default menus if none exist
   */
  async initializeDefaultMenus(): Promise<void> {
    const count = await this.menuRepository.count();
    if (count === 0) {
      // Create default menus
      const defaultMenus = [
        // Root menus
        { name: '首页', path: '/dashboard', icon: 'home', orderNum: 1 },
        { name: '系统管理', path: '/system', icon: 'settings', orderNum: 2 },
        { name: '系统监控', path: '/monitor', icon: 'activity', orderNum: 3 },
        { name: '系统工具', path: '/tool', icon: 'wrench', orderNum: 4 },

        // System management submenus
        { name: '用户管理', path: '/system/user', icon: 'user', parentId: 2, orderNum: 1 },
        { name: '角色管理', path: '/system/role', icon: 'users', parentId: 2, orderNum: 2 },
        { name: '菜单管理', path: '/system/menu', icon: 'menu', parentId: 2, orderNum: 3 },

        // System monitoring submenus
        { name: '在线用户', path: '/monitor/online', icon: 'user-check', parentId: 3, orderNum: 1 },
        { name: '服务监控', path: '/monitor/server', icon: 'server', parentId: 3, orderNum: 2 },

        // System tools submenus
        { name: '代码生成', path: '/tool/gen', icon: 'code', parentId: 4, orderNum: 1 },
        { name: '系统接口', path: '/tool/swagger', icon: 'file-text', parentId: 4, orderNum: 2 },
      ];

      for (const menu of defaultMenus) {
        await this.create(menu);
      }
    }
  }
}
