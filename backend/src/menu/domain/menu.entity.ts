import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';

@Entity('menus')
export class Menu {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  path: string;

  @Column({ nullable: true })
  component: string;

  @Column({ nullable: true })
  icon: string;

  @Column({ default: 0 })
  orderNum: number;

  @Column({ nullable: true })
  parentId: number;

  @ManyToOne(() => Menu, menu => menu.children)
  @JoinColumn({ name: 'parent_id' })
  parent: <PERSON>u;

  @OneToMany(() => Menu, menu => menu.parent)
  children: Menu[];

  @Column({ default: true })
  status: boolean;

  @Column({ default: false })
  hidden: boolean;

  @Column({ nullable: true, unique: true })
  resourceCode: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
