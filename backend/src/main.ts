import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { NextModule } from './next/next.module';
import { MenuService } from './menu/menu.service';
import { DepartmentService } from './department/department.service';
import { join } from 'path';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    transformOptions: {
      enableImplicitConversion: true,
    },
    whitelist: true,
    forbidNonWhitelisted: false,
  }));

  // Set global prefix for all routes except the RootController
  app.setGlobalPrefix('api', { exclude: ['/'] });

  // Setup Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Mini Shop API')
    .setDescription('The Mini Shop API documentation')
    .setVersion('1.0')
    .addTag('users')
    .addTag('auth')
    .addTag('sms')
    .addTag('menus')
    .addTag('departments')
    .addTag('dictionary')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config, {
    extraModels: [],
    deepScanRoutes: true,
  });
  SwaggerModule.setup('docs', app, document);

  // Enable CORS
  app.enableCors();

  // Initialize menu data
  const menuService = app.get(MenuService);
  await menuService.initializeDefaultMenus();

  // Initialize department data
  const departmentService = app.get(DepartmentService);
  await departmentService.initializeDefaultDepartments();

  // We don't need to manually compile Tailwind CSS
  // Next.js will handle CSS processing automatically

  // Initialize Next.js before starting the server
  try {
    logger.log('Next.js app is initializing...');
    await app
      .get(NextModule)
      .prepare()
      .then(() => {
        app.listen(3000);
        logger.log('NestJS server with Next.js middleware is running on http://localhost:3000');
      });
  } catch (error) {
    logger.error(`Failed to initialize Next.js: ${error.message}`);
    process.exit(1);
  }
}
bootstrap();