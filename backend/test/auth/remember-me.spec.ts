import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from '../../src/auth/auth.service';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../../src/entities/user.entity';
import { UserAuth } from '../../src/entities/user-auth.entity';
import { LoginDto } from '../../src/dto/auth.dto';
import { AuthType } from '@common/enums/auth-type.enum';
import { UnauthorizedException } from '@nestjs/common';
import * as bcrypt from 'bcryptjs';

// Mock bcrypt
jest.mock('bcryptjs', () => ({
  compare: jest.fn().mockResolvedValue(true),
  genSalt: jest.fn().mockResolvedValue('salt'),
  hash: jest.fn().mockResolvedValue('hashed-password'),
}));

describe('AuthService - Remember Me', () => {
  let service: AuthService;
  let userRepository;
  let userAuthRepository;
  let jwtService;

  // Mock user data
  const mockUser = {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    phone: null,
    token: 'mock-token',
    status: 'active',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=testuser',
    refreshToken: null,
    refreshTokenExpiresAt: null,
    save: jest.fn(),
  };

  // Mock user auth data
  const mockUserAuth = {
    id: 1,
    user: mockUser,
    account: 'testuser',
    authType: AuthType.PASSWORD,
    credential: 'hashed-password',
  };

  beforeEach(async () => {
    // Create mock repositories
    userRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    userAuthRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    // Create mock JWT service
    jwtService = {
      sign: jest.fn().mockReturnValue('mock-token'),
      verify: jest.fn(),
    };

    // Create test module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: userRepository,
        },
        {
          provide: getRepositoryToken(UserAuth),
          useValue: userAuthRepository,
        },
        {
          provide: JwtService,
          useValue: jwtService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
  });

  describe('login with rememberMe', () => {
    it('should store refreshToken in database when rememberMe is true', async () => {
      // Arrange
      const loginDto: LoginDto = {
        account: 'testuser',
        authCredential: 'password123',
        authType: AuthType.PASSWORD,
        rememberMe: true,
      };

      userAuthRepository.findOne.mockResolvedValue(mockUserAuth);
      userRepository.save.mockResolvedValue(mockUser);

      // Act
      const result = await service.login(loginDto);

      // Assert
      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          refreshToken: 'mock-token',
          refreshTokenExpiresAt: expect.any(Date),
        }),
      );
      expect(result.accessToken).toBe('mock-token');
      expect(result.refreshToken).toBe('mock-token');
    });

    it('should not store refreshToken in database when rememberMe is false', async () => {
      // Arrange
      const loginDto: LoginDto = {
        account: 'testuser',
        authCredential: 'password123',
        authType: AuthType.PASSWORD,
        rememberMe: false,
      };

      userAuthRepository.findOne.mockResolvedValue(mockUserAuth);
      userRepository.save.mockResolvedValue(mockUser);

      // Act
      const result = await service.login(loginDto);

      // Assert
      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          refreshToken: null,
          refreshTokenExpiresAt: null,
        }),
      );
      expect(result.accessToken).toBe('mock-token');
      expect(result.refreshToken).toBe('mock-token');
    });
  });

  describe('refreshToken', () => {
    it('should verify stored refreshToken when refreshing', async () => {
      // Arrange
      const storedRefreshToken = 'stored-refresh-token';
      const mockUserWithRefreshToken = {
        ...mockUser,
        refreshToken: storedRefreshToken,
        refreshTokenExpiresAt: new Date(Date.now() + ********), // 1 day in the future
      };

      jwtService.verify.mockReturnValue({
        sub: '1',
        username: 'testuser',
        authType: AuthType.PASSWORD,
      });

      userRepository.findOne.mockResolvedValue(mockUserWithRefreshToken);
      userRepository.save.mockResolvedValue(mockUserWithRefreshToken);

      // Act
      const result = await service.refreshToken(storedRefreshToken);

      // Assert
      expect(result.accessToken).toBe('mock-token');
      expect(result.refreshToken).toBe('mock-token');
      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          refreshToken: 'mock-token',
        }),
      );
    });

    it('should throw error when refreshToken does not match stored token', async () => {
      // Arrange
      const storedRefreshToken = 'stored-refresh-token';
      const providedRefreshToken = 'different-refresh-token';
      const mockUserWithRefreshToken = {
        ...mockUser,
        refreshToken: storedRefreshToken,
        refreshTokenExpiresAt: new Date(Date.now() + ********), // 1 day in the future
      };

      jwtService.verify.mockReturnValue({
        sub: '1',
        username: 'testuser',
        authType: AuthType.PASSWORD,
      });

      userRepository.findOne.mockResolvedValue(mockUserWithRefreshToken);

      // Act & Assert
      await expect(service.refreshToken(providedRefreshToken)).rejects.toThrow(
        new UnauthorizedException('Refresh token does not match'),
      );
    });

    it('should throw error when refreshToken is expired', async () => {
      // Arrange
      const storedRefreshToken = 'stored-refresh-token';
      const mockUserWithExpiredToken = {
        ...mockUser,
        refreshToken: storedRefreshToken,
        refreshTokenExpiresAt: new Date(Date.now() - ********), // 1 day in the past
      };

      jwtService.verify.mockReturnValue({
        sub: '1',
        username: 'testuser',
        authType: AuthType.PASSWORD,
      });

      userRepository.findOne.mockResolvedValue(mockUserWithExpiredToken);
      userRepository.save.mockResolvedValue({
        ...mockUserWithExpiredToken,
        refreshToken: null,
        refreshTokenExpiresAt: null,
      });

      // Act & Assert
      await expect(service.refreshToken(storedRefreshToken)).rejects.toThrow(
        new UnauthorizedException('Refresh token has expired'),
      );
      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          refreshToken: null,
          refreshTokenExpiresAt: null,
        }),
      );
    });
  });
});
