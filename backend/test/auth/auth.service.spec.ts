import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from '../../src/auth/auth.service';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
// 创建模拟实体和 DTO，避免依赖外部模块

// 模拟枚举类型
enum AuthType {
  PASSWORD = 'password',
  GOOGLE = 'google',
}

enum LoginType {
  PASSWORD = 'password',
  GOOGLE = 'google',
}

class User {
  id: number;
  username: string;
  email: string;
  phone: string;
  token: string;
  status: string;
  avatar: string;
  loginType: LoginType;
  thirdPartyId: string;
  refreshToken: string;
  refreshTokenExpiresAt: Date;
  authMethods: any[];
  createdAt: Date;
  updatedAt: Date;
}

class UserAuth {
  id: number;
  user: User;
  account: string;
  authType: AuthType;
  credential: string;
}

interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: User;
  isNewUser: boolean;
}

class LoginDto {
  account: string;
  authCredential: string;
  authType: AuthType;
}
import { UnauthorizedException } from '@nestjs/common';
import * as bcrypt from 'bcryptjs';

// 模拟 bcrypt
jest.mock('bcryptjs', () => ({
  compare: jest.fn().mockResolvedValue(true),
  genSalt: jest.fn().mockResolvedValue('salt'),
  hash: jest.fn().mockResolvedValue('hashed-password'),
}));

describe('AuthService', () => {
  let service: AuthService;
  let userRepository;
  let userAuthRepository;
  let jwtService;

  // 模拟数据
  const mockUser = new User();
  mockUser.id = 1;
  mockUser.username = 'testuser';
  mockUser.email = '<EMAIL>';
  mockUser.phone = null;
  mockUser.token = 'mock-token';
  mockUser.status = 'active';
  mockUser.avatar = 'https://api.dicebear.com/7.x/avataaars/svg?seed=testuser';
  mockUser.loginType = LoginType.PASSWORD;
  mockUser.thirdPartyId = null;
  mockUser.refreshToken = null;
  mockUser.refreshTokenExpiresAt = null;
  mockUser.authMethods = [];
  mockUser.createdAt = new Date();
  mockUser.updatedAt = new Date();

  const mockUserAuth = new UserAuth();
  mockUserAuth.id = 1;
  mockUserAuth.user = mockUser;
  mockUserAuth.account = '<EMAIL>';
  mockUserAuth.authType = AuthType.PASSWORD;
  mockUserAuth.credential = 'hashed-password';

  beforeEach(async () => {
    // 创建模拟存储库
    userRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    userAuthRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    // 创建模拟 JWT 服务
    jwtService = {
      sign: jest.fn().mockReturnValue('mock-token'),
      verify: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: userRepository,
        },
        {
          provide: getRepositoryToken(UserAuth),
          useValue: userAuthRepository,
        },
        {
          provide: JwtService,
          useValue: jwtService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);

    // 重置所有模拟函数
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('login', () => {
    // 测试场景 1: userAuth 不存在
    it('should throw UnauthorizedException when userAuth does not exist', async () => {
      // 模拟 userAuth 不存在
      userAuthRepository.findOne.mockResolvedValue(null);

      const loginDto = {
        account: '<EMAIL>',
        authCredential: 'password123',
        authType: AuthType.PASSWORD,
      };

      // 验证抛出 UnauthorizedException
      await expect(service.login(loginDto)).rejects.toThrow(
        new UnauthorizedException('User authentication record not found'),
      );
      expect(userAuthRepository.findOne).toHaveBeenCalledWith({
        where: { account: loginDto.account, authType: loginDto.authType },
      });
    });

    // 测试场景 2: 凭证无效
    it('should throw UnauthorizedException when credentials are invalid', async () => {
      // 模拟 userAuth 存在
      userAuthRepository.findOne.mockResolvedValue(mockUserAuth);

      // 模拟凭证验证失败
      jest.spyOn(service as any, 'validateCredential').mockResolvedValueOnce(false);

      const loginDto = {
        account: '<EMAIL>',
        authCredential: 'wrong-password',
        authType: AuthType.PASSWORD,
      };

      // 验证抛出 UnauthorizedException
      await expect(service.login(loginDto)).rejects.toThrow(
        new UnauthorizedException('Invalid credentials'),
      );
    });

    // 测试场景 3: userAuth 存在但 user 不存在，创建新用户
    it('should create a new user when userAuth exists but user does not', async () => {
      // 模拟 userAuth 存在但 user 不存在
      const userAuthWithoutUser = { ...mockUserAuth, user: null };
      userAuthRepository.findOne.mockResolvedValue(userAuthWithoutUser);
      userRepository.findOne.mockResolvedValue(null);

      // 模拟保存用户和更新 userAuth
      userRepository.save.mockImplementation(user => {
        // 修改原始对象，模拟 TypeORM 的行为
        user.id = 1;
        return user;
      });
      userAuthRepository.save.mockResolvedValue(mockUserAuth);

      const loginDto = {
        account: '<EMAIL>',
        authCredential: 'password123',
        authType: AuthType.PASSWORD,
      };

      const result = await service.login(loginDto);

      // 验证结果
      expect(result.isNewUser).toBe(true);
      expect(userRepository.save).toHaveBeenCalled();
      expect(userAuthRepository.save).toHaveBeenCalled();

      // 验证创建的用户属性
      const savedUser = userRepository.save.mock.calls[0][0];
      expect(savedUser.username).toBe(loginDto.account);
      expect(savedUser.email).toBe(loginDto.account); // 因为包含 @
      expect(savedUser.avatar).toBe(`https://api.dicebear.com/7.x/avataaars/svg?seed=${loginDto.account}`);

      // 验证更新的 userAuth
      const savedUserAuth = userAuthRepository.save.mock.calls[0][0];
      expect(savedUserAuth.user.id).toBe(1);
    });

    // 测试场景 4: userAuth 存在且 user 也存在，直接登录
    it('should login directly when both userAuth and user exist', async () => {
      // 模拟 userAuth 和 user 都存在
      userAuthRepository.findOne.mockResolvedValue(mockUserAuth);
      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue(mockUser);

      const loginDto = {
        account: '<EMAIL>',
        authCredential: 'password123',
        authType: AuthType.PASSWORD,
      };

      const result = await service.login(loginDto);

      // 验证结果
      expect(result.isNewUser).toBe(false);
      expect(userRepository.save).toHaveBeenCalledTimes(1); // 只保存一次，更新 token
      expect(userAuthRepository.save).not.toHaveBeenCalled(); // 不需要保存 userAuth
      expect(result.accessToken).toBe('mock-token');
      expect(result.refreshToken).toBe('mock-token');
      expect(result.user).toEqual(mockUser);

      // 验证 JWT payload
      expect(jwtService.sign).toHaveBeenCalledWith({
        sub: mockUser.id.toString(),
        username: mockUser.username,
        authType: loginDto.authType,
      });
    });
  });

  describe('refreshToken', () => {
    it('should refresh the token when a valid refresh token is provided', async () => {
      // 模拟 JWT 验证
      jwtService.verify.mockReturnValue({
        sub: '1',
        username: 'testuser',
        authType: 'password',
      });

      // 模拟查找用户
      userRepository.findOne.mockResolvedValue(mockUser);
      userRepository.save.mockResolvedValue(mockUser);

      const result = await service.refreshToken('valid-refresh-token');

      // 验证结果
      expect(result.accessToken).toBe('mock-token');
      expect(result.refreshToken).toBe('mock-token');
      expect(result.user).toEqual(mockUser);
      expect(result.isNewUser).toBe(false);
    });

    it('should throw UnauthorizedException when refresh token is invalid', async () => {
      // 模拟 JWT 验证失败
      jwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // 验证抛出 UnauthorizedException
      await expect(service.refreshToken('invalid-refresh-token')).rejects.toThrow(
        new UnauthorizedException('Invalid refresh token'),
      );
    });

    it('should throw UnauthorizedException when user not found', async () => {
      // 模拟 JWT 验证
      jwtService.verify.mockImplementation(() => ({
        sub: '999', // 不存在的用户 ID
        username: 'nonexistent',
        authType: 'password',
      }));

      // 模拟用户不存在
      userRepository.findOne.mockResolvedValue(null);

      // 直接模拟抛出用户不存在的错误
      await expect(service.refreshToken('valid-refresh-token')).rejects.toThrow(
        'User not found'
      );
    });
  });
});
