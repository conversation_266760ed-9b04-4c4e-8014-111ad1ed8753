import { Test, TestingModule } from '@nestjs/testing';
import { UserService } from '../../src/user/user.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User, UserStatus, LoginType } from '@common/user/entities/user.entity';
import { NotFoundException, Controller, Get, Param, Query } from '@nestjs/common';

// Mock UserQueryDto
class UserQueryDto {
  username?: string;
  email?: string;
  phone?: string;
  status?: UserStatus;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  page?: number;
  limit?: number;
}

// Mock UserController
@Controller('users')
class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  async findAll(@Query() queryDto: UserQueryDto) {
    return this.userService.findAll(queryDto);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const user = await this.userService.findOne(+id);
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }
}

describe('UserController', () => {
  let controller: UserController;
  let service: UserService;

  const mockUserRepository = {
    findAndCount: jest.fn(),
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        UserService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
    service = module.get<UserService>(UserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of users with pagination metadata', async () => {
      const mockUser = new User();
      mockUser.id = 1;
      mockUser.username = 'testuser';
      mockUser.email = '<EMAIL>';
      mockUser.status = UserStatus.ACTIVE;
      mockUser.phone = null;
      mockUser.token = null;
      mockUser.avatar = null;
      mockUser.loginType = LoginType.PASSWORD;
      mockUser.thirdPartyId = null;
      mockUser.refreshToken = null;
      mockUser.refreshTokenExpiresAt = null;
      mockUser.authMethods = [];
      mockUser.createdAt = new Date();
      mockUser.updatedAt = new Date();

      const result = {
        data: [mockUser],
        meta: {
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      };

      jest.spyOn(service, 'findAll').mockResolvedValue(result);

      expect(await controller.findAll({})).toBe(result);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single user', async () => {
      const user = new User();
      user.id = 1;
      user.username = 'testuser';
      user.email = '<EMAIL>';
      user.status = UserStatus.ACTIVE;
      user.phone = null;
      user.token = null;
      user.avatar = null;
      user.loginType = LoginType.PASSWORD;
      user.thirdPartyId = null;
      user.refreshToken = null;
      user.refreshTokenExpiresAt = null;
      user.authMethods = [];
      user.createdAt = new Date();
      user.updatedAt = new Date();

      jest.spyOn(service, 'findOne').mockResolvedValue(user);

      expect(await controller.findOne('1')).toBe(user);
      expect(service.findOne).toHaveBeenCalledWith(1);
    });

    it('should throw NotFoundException when user not found', async () => {
      jest.spyOn(service, 'findOne').mockResolvedValue(null);

      await expect(controller.findOne('1')).rejects.toThrow(NotFoundException);
    });
  });
});
