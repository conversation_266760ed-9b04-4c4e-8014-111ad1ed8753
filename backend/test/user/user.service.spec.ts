import { Test, TestingModule } from '@nestjs/testing';
import { UserService } from '../../src/user/user.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User, UserStatus, LoginType } from '@common/user/entities/user.entity';
import { Repository } from 'typeorm';

describe('UserService', () => {
  let service: UserService;
  let repository: Repository<User>;

  const createMockUser = (id: number, username: string, email: string, status: UserStatus) => {
    const user = new User();
    user.id = id;
    user.username = username;
    user.email = email;
    user.status = status;
    user.phone = null;
    user.token = null;
    user.avatar = null;
    user.loginType = LoginType.PASSWORD;
    user.thirdPartyId = null;
    user.refreshToken = null;
    user.refreshTokenExpiresAt = null;
    user.authMethods = [];
    user.createdAt = new Date();
    user.updatedAt = new Date();
    return user;
  };

  const mockUsers = [
    createMockUser(1, 'testuser1', '<EMAIL>', UserStatus.ACTIVE),
    createMockUser(2, 'testuser2', '<EMAIL>', UserStatus.INACTIVE),
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getRepositoryToken(User),
          useValue: {
            findAndCount: jest.fn().mockResolvedValue([mockUsers, 2]),
            findOne: jest.fn().mockImplementation((options) => {
              const id = options.where.id;
              const user = mockUsers.find(u => u.id === id);
              return Promise.resolve(user);
            }),
          },
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of users with pagination metadata', async () => {
      const result = await service.findAll({});

      expect(result.data).toEqual(mockUsers);
      expect(result.meta.total).toEqual(2);
      expect(repository.findAndCount).toHaveBeenCalled();
    });

    it('should apply filters when provided', async () => {
      await service.findAll({ username: 'test', status: UserStatus.ACTIVE });

      expect(repository.findAndCount).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            username: expect.any(Object),
            status: UserStatus.ACTIVE,
          }),
        }),
      );
    });
  });

  describe('findOne', () => {
    it('should return a single user', async () => {
      const result = await service.findOne(1);

      expect(result).toEqual(mockUsers[0]);
      expect(repository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
    });

    it('should return null when user not found', async () => {
      jest.spyOn(repository, 'findOne').mockResolvedValueOnce(null);

      const result = await service.findOne(999);

      expect(result).toBeNull();
    });
  });

  describe('findByUsername', () => {
    it('should find a user by username', async () => {
      jest.spyOn(repository, 'findOne').mockResolvedValueOnce(mockUsers[0]);

      const result = await service.findByUsername('testuser1');

      expect(result).toEqual(mockUsers[0]);
      expect(repository.findOne).toHaveBeenCalledWith({ where: { username: 'testuser1' } });
    });
  });
});
