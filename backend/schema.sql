CREATE TYPE "user_auths_authtype_enum" AS ENUM('password', 'phone', 'wechat', 'github', 'google', 'sms');
CREATE TABLE "user_auths" ("id" SERIAL NOT NULL, "auth_type" "user_auths_authtype_enum" NOT NULL, "account" character varying, "credential" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "expire" TIMESTAMP, "user_id" integer, CONSTRAINT "PK_c72509696bfcdb831991f01d4c1" PRIMARY KEY ("id"));
CREATE TYPE "users_status_enum" AS ENUM('active', 'inactive', 'banned');
CREATE TYPE "users_logintype_enum" AS ENUM('password', 'wechat', 'phone', 'github', 'google');
CREATE TABLE "users" ("id" SERIAL NOT NULL, "username" character varying NOT NULL, "email" character varying, "phone" character varying, "token" character varying, "avatar" character varying, "status" "users_status_enum" NOT NULL DEFAULT 'active', "login_type" "users_logintype_enum" NOT NULL DEFAULT 'password', "third_party_id" character varying, "refresh_token" character varying, "refresh_token_expires_at" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_fe0bb3f6520ee0469504521e710" UNIQUE ("username"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"));
CREATE TABLE "departments" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "description" character varying, "order_num" integer NOT NULL DEFAULT '0', "parent_id" integer, "status" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "parent_id" integer, CONSTRAINT "PK_839517a681a86bb84cbcc6a1e9d" PRIMARY KEY ("id"));
CREATE TABLE "menus" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "path" character varying, "component" character varying, "icon" character varying, "order_num" integer NOT NULL DEFAULT '0', "parent_id" integer, "status" boolean NOT NULL DEFAULT true, "hidden" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "parent_id" integer, CONSTRAINT "PK_3fec3d93327f4538e0cbd4349c4" PRIMARY KEY ("id"));
ALTER TABLE "user_auths" ADD CONSTRAINT "FK_4a4582307bc3a80b309a207adb6" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "departments" ADD CONSTRAINT "FK_700b0b13f494cb37b6ca929e79b" FOREIGN KEY ("parent_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "menus" ADD CONSTRAINT "FK_00ccc1ed4e9fc23bc1246269359" FOREIGN KEY ("parent_id") REFERENCES "menus"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
