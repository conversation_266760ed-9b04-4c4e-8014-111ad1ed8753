{"extends": "./tsconfig.paths.json", "compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"@common/*": ["../common/src/*"], "@frontend/*": ["../frontend/*"], "class-validator": ["node_modules/class-validator"], "class-transformer": ["node_modules/class-transformer"], "typeorm": ["node_modules/typeorm"]}, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false}}