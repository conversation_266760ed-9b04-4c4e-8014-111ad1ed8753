#!/usr/bin/env node

const { execSync } = require('child_process');

/**
 * Run a command and log its output
 * @param {string} command - The command to run
 * @param {string} step - The step description
 */
function runCommand(command, step) {
  console.log(`\n\x1b[36m${step}\x1b[0m`);
  console.log(`Running: ${command}\n`);

  try {
    execSync(command, { stdio: 'inherit' });
  } catch (error) {
    console.error(`\x1b[31mError during ${step}:\x1b[0m`, error.message);
    process.exit(1);
  }
}

// Main function to run the migration process
async function runMigration() {
  // Check if dry-run flag is provided
  const isDryRun = process.argv.includes('--dry-run');
  const dryRunFlag = isDryRun ? ' --dry-run' : '';

  console.log(`\x1b[32m=== Starting Database Migration Process ${isDryRun ? '(DRY RUN)' : ''} ===\x1b[0m\n`);

  try {
    // Step 1: Generate schema.sql and create migration file
    runCommand('node ./scripts/generate-migration.js', 'Step 1: Generating schema and migration files');

    // Step 2: Apply the migration to the database (with optional dry-run flag)
    if (isDryRun) {
      runCommand('npm run migration:apply -- --dry-run', 'Step 2: Simulating migration application (DRY RUN)');
    } else {
      runCommand('npm run migration:apply', 'Step 2: Applying the migration to the database');
    }

    if (isDryRun) {
      console.log('\n\x1b[33m=== Migration Dry Run Completed Successfully! ===\x1b[0m');
      console.log('\x1b[33mNo changes were applied to the database. Run without --dry-run to apply changes.\x1b[0m');
    } else {
      console.log('\n\x1b[32m=== Migration Process Completed Successfully! ===\x1b[0m');
    }
  } catch (error) {
    console.error('\x1b[31m=== Migration Process Failed! ===\x1b[0m');
    process.exit(1);
  }
}

// Run the migration process
runMigration();
