#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Generate a timestamp for migration file names
 * @returns {string} Timestamp in format YYYYMMDDHHMMSS
 */
function generateTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

/**
 * Check if a database exists and has tables
 * @param {string} connectionString - Database connection string
 * @returns {boolean} True if database exists and has tables
 */
function databaseHasTables(connectionString) {
  try {
    // Query to check if any tables exist in the public schema
    const query = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'";
    const result = execSync(`psql "${connectionString}" -t -c "${query}"`, { encoding: 'utf8' });
    const count = parseInt(result.trim(), 10);
    return count > 0;
  } catch (error) {
    console.error('Error checking database tables:', error.message);
    return false;
  }
}

/**
 * Generate a migration file from schema.sql
 */
function generateMigration() {
  try {
    console.log('Generating schema.sql from TypeORM entities...');
    execSync('npm run migration:schema', { stdio: 'inherit' });

    // Read the generated schema.sql
    const schemaPath = path.join(__dirname, '..', 'schema.sql');
    if (!fs.existsSync(schemaPath)) {
      throw new Error('schema.sql not found. Make sure migration:schema script is working correctly.');
    }

    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');

    // Check if target database already has tables
    const targetConnectionString = "postgres://demo_shop_user:Demo_shop%402024_Secure@**************:5432/demo_shop?sslmode=disable";
    const hasExistingTables = databaseHasTables(targetConnectionString);

    if (hasExistingTables) {
      console.log('Target database already has tables. Using Atlas migrate diff to generate migration...');
      // Use Atlas migrate diff to generate migration
      execSync('npm run migration:generate', { stdio: 'inherit' });
    } else {
      console.log('Target database is empty. Creating initial migration from schema.sql...');

      // Create a new migration file with the schema SQL
      const timestamp = generateTimestamp();
      const migrationFileName = `${timestamp}_initial_schema.sql`;
      const migrationPath = path.join(__dirname, '..', 'sql', migrationFileName);

      // Add comments and write the SQL to the migration file
      const migrationContent = `-- Initial schema migration
-- Generated from TypeORM entities with snake_case column names

${schemaSQL}`;

      fs.writeFileSync(migrationPath, migrationContent);
      console.log(`Created migration file: ${migrationFileName}`);

      // Update the atlas.sum file
      console.log('Updating atlas.sum file...');
      execSync('npx @ariga/atlas migrate hash --dir "file://sql"', { stdio: 'inherit' });
    }

    console.log('Migration generation completed successfully!');
  } catch (error) {
    console.error('Error generating migration:', error.message);
    process.exit(1);
  }
}

// Run the migration generation
generateMigration();
