# Scripts Directory

This directory contains utility scripts for the backend application.

## Migration Scripts

### run-migration.js

This script provides a simplified workflow for database migrations with snake_case column naming convention. It combines three separate migration steps into a single command:

1. Generate schema.sql with snake_case columns
2. Create a new migration based on schema differences
3. Apply the migration to the database

#### Usage

```bash
# Apply all migrations
npm run migration

# Preview migrations without applying them (dry run)
npm run migration -- --dry-run
```

#### Features

- Automatically converts all entity property names from camelCase to snake_case in the database
- Provides a single command for the entire migration workflow
- Supports dry run mode to preview changes without applying them
- Maintains consistent naming conventions across the entire database
