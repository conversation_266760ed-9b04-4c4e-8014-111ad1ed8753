{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"_comment1": "Application lifecycle scripts", "start": "node dist/backend/src/main.js", "start:dev": "node dist/backend/src/main.js", "start:debug": "nest start --debug --watch", "clean": "<PERSON><PERSON><PERSON> dist", "build:nest": "nest build", "build": "npm run clean && cd ../frontend && npm run build && cd ../backend && npm run build:nest", "_comment2": "Code quality scripts", "lint": "eslint \"src/**/*.{js,ts}\" --max-warnings 0", "lint:fix": "eslint \"src/**/*.{js,ts}\" --fix", "format": "prettier --write \"src/**/*.{js,ts,json}\"", "format:check": "prettier --check \"src/**/*.{js,ts,json}\"", "_comment3": "Database migration and seeding scripts", "migration": "node ./scripts/run-migration.js", "migration:schema": "node atlas-provider.js", "migration:generate": "npx @ariga/atlas migrate diff --dir \"file://sql\" --dev-url \"postgres://demo_shop_user:Demo_shop%402024_Secure@**************:5432/demo_shop_dev?sslmode=disable\" --to \"postgres://demo_shop_user:Demo_shop%402024_Secure@**************:5432/demo_shop?sslmode=disable\"", "migration:apply": "npx @ariga/atlas migrate apply --dir \"file://sql\" -u \"postgres://demo_shop_user:Demo_shop%402024_Secure@**************:5432/demo_shop?sslmode=disable\" --allow-dirty", "migration:create": "node ./scripts/generate-migration.js", "seed": "npx ts-node -r tsconfig-paths/register src/database/seed.ts", "seed:admin": "npx ts-node -r tsconfig-paths/register src/database/create-admin-user.ts", "_comment4": "Code generation scripts", "generate:openapi": "npx ts-node -r tsconfig-paths/register src/openapi-generator.ts", "_comment5": "Testing scripts", "test": "jest --config ./jest.config.js", "test:watch": "jest --config ./jest.config.js --watch", "test:cov": "jest --config ./jest.config.js --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --config ./jest.config.js --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@alicloud/credentials": "^2.4.3", "@alicloud/dysmsapi20170525": "^4.0.0", "@alicloud/openapi-client": "^0.4.14", "@alicloud/tea-util": "^1.4.10", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.2.10", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.3.0", "@nestjs/typeorm": "^10.0.1", "@nestjs/websockets": "^10.2.10", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "next": "15.2.4", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.10.0", "react": "19.0.0", "react-dom": "19.0.0", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.5", "rxjs": "^7.8.1", "svg-captcha": "^1.4.0", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17"}, "devDependencies": {"@ariga/atlas-provider-typeorm": "^0.2.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.6", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/js-yaml": "^4.0.9", "@types/node": "^20.5.9", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "js-yaml": "^4.1.0", "prettier": "^3.2.5", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2", "webpack": "^5.97.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}