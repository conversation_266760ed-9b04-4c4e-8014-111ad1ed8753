-- Create dictionary_entries table
CREATE TABLE "dictionary_entries" (
    "id" SERIAL NOT NULL,
    "key" character varying NOT NULL,
    "description" character varying,
    "enabled" boolean NOT NULL DEFAULT true,
    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
    CONSTRAINT "UQ_dictionary_entries_key" UNIQUE ("key"),
    CONSTRAINT "PK_dictionary_entries" PRIMARY KEY ("id")
);

-- Create dictionary_values table
CREATE TABLE "dictionary_values" (
    "id" SERIAL NOT NULL,
    "value" character varying NOT NULL,
    "label" character varying,
    "order_num" integer NOT NULL DEFAULT 0,
    "entry_id" integer NOT NULL,
    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
    CONSTRAINT "PK_dictionary_values" PRIMARY KEY ("id")
);

-- Add foreign key constraint
ALTER TABLE "dictionary_values" 
ADD CONSTRAINT "FK_dictionary_values_entry_id" 
FOREIGN KEY ("entry_id") 
REFERENCES "dictionary_entries"("id") 
ON DELETE CASCADE 
ON UPDATE NO ACTION;
