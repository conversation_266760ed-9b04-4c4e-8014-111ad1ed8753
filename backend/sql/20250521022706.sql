-- Add new schema named "atlas_schema_revisions"
CREATE SCHEMA "atlas_schema_revisions";
-- <PERSON><PERSON> enum type "users_status_enum"
CREATE TYPE "public"."users_status_enum" AS ENUM ('active', 'inactive', 'banned');
-- <PERSON>reate enum type "users_logintype_enum"
CREATE TYPE "public"."users_logintype_enum" AS ENUM ('password', 'wechat', 'phone', 'github', 'google');
-- Create enum type "user_auths_authtype_enum"
CREATE TYPE "public"."user_auths_authtype_enum" AS ENUM ('password', 'phone', 'wechat', 'github', 'google', 'sms_verification', 'sms');
-- Create "departments" table
CREATE TABLE "public"."departments" ("id" serial NOT NULL, "name" character varying NOT NULL, "description" character varying NULL, "order_num" integer NOT NULL DEFAULT 0, "parent_id" integer NULL, "status" boolean NOT NULL DEFAULT true, "created_at" timestamp NOT NULL DEFAULT now(), "updated_at" timestamp NOT NULL DEFAULT now(), CONSTRAINT "PK_839517a681a86bb84cbcc6a1e9d" PRIMARY KEY ("id"), CONSTRAINT "FK_2c0c254d34be97f6982d3138fc7" FOREIGN KEY ("parent_id") REFERENCES "public"."departments" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "menus" table
CREATE TABLE "public"."menus" ("id" serial NOT NULL, "name" character varying NOT NULL, "path" character varying NULL, "component" character varying NULL, "icon" character varying NULL, "order_num" integer NOT NULL DEFAULT 0, "parent_id" integer NULL, "status" boolean NOT NULL DEFAULT true, "hidden" boolean NOT NULL DEFAULT false, "created_at" timestamp NOT NULL DEFAULT now(), "updated_at" timestamp NOT NULL DEFAULT now(), CONSTRAINT "PK_3fec3d93327f4538e0cbd4349c4" PRIMARY KEY ("id"), CONSTRAINT "FK_8523e13f1ba719e16eb474657ec" FOREIGN KEY ("parent_id") REFERENCES "public"."menus" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION);
-- Create "users" table
CREATE TABLE "public"."users" ("id" serial NOT NULL, "username" character varying NOT NULL, "email" character varying NULL, "phone" character varying NULL, "token" character varying NULL, "avatar" character varying NULL, "status" "public"."users_status_enum" NOT NULL DEFAULT 'active', "login_type" "public"."users_logintype_enum" NOT NULL DEFAULT 'password', "third_party_id" character varying NULL, "refresh_token" character varying NULL, "refresh_token_expires_at" timestamp NULL, "created_at" timestamp NOT NULL DEFAULT now(), "updated_at" timestamp NOT NULL DEFAULT now(), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"), CONSTRAINT "UQ_fe0bb3f6520ee0469504521e710" UNIQUE ("username"));
-- Create "atlas_schema_revisions" table
CREATE TABLE "atlas_schema_revisions"."atlas_schema_revisions" ("version" character varying NOT NULL, "description" character varying NOT NULL, "type" bigint NOT NULL DEFAULT 2, "applied" bigint NOT NULL DEFAULT 0, "total" bigint NOT NULL DEFAULT 0, "executed_at" timestamptz NOT NULL, "execution_time" bigint NOT NULL, "error" text NULL, "error_stmt" text NULL, "hash" character varying NOT NULL, "partial_hashes" jsonb NULL, "operator_version" character varying NOT NULL, PRIMARY KEY ("version"));
-- Create "user_auths" table
CREATE TABLE "public"."user_auths" ("id" serial NOT NULL, "auth_type" "public"."user_auths_authtype_enum" NOT NULL, "account" character varying NULL, "credential" character varying NULL, "created_at" timestamp NOT NULL DEFAULT now(), "updated_at" timestamp NOT NULL DEFAULT now(), "expire" timestamp NULL, "user_id" integer NULL, CONSTRAINT "PK_c72509696bfcdb831991f01d4c1" PRIMARY KEY ("id"), CONSTRAINT "FK_cfa0eb5c2e790fb0ca945276569" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE CASCADE);
