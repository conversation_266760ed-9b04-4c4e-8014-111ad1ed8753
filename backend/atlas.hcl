data "external_schema" "typeorm" {
  program = [
    "node",
    "atlas-provider.js"
  ]
  // This program uses a custom script to ensure all column names are in snake_case
}

env "typeorm" {
  // This is your desired schema (from TypeORM entities)
  src = data.external_schema.typeorm.url
  // This is your development database schema (clean database)
  dev = "*******************************************************************/demo_shop_dev?sslmode=disable"
  url = "*******************************************************************/demo_shop?sslmode=disable"

  migration {
    dir = "file://sql"
  }

  format {
    migrate {
      diff = "{{ sql . \"  \" }}"
    }
  }
}