# Mini Shop - NestJS + Next.js 集成项目

这个项目展示了如何在同一个进程中集成 NestJS 和 Next.js，通过单一端口提供服务。

## 项目结构

- `backend/` - NestJS 后端应用
- `frontend/` - Next.js 前端应用
- `common/` - 共享的代码和实体

## 技术栈

- **后端**: NestJS, TypeORM, PostgreSQL
- **前端**: Next.js, React

## 特性

- 单进程运行 NestJS 和 Next.js
- 所有请求通过同一个端口（默认 3000）处理
- NestJS 提供 API 接口 (`/api/**`)
- Next.js 提供前端页面 (`/`, `/about` 等)

## 安装与配置

1. 安装依赖:

```bash
# 安装所有依赖
npm install
```

2. 配置环境变量:

在 `backend/` 目录中创建 `.env` 文件，并添加必要的环境变量：

```
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=mini_shop
```

## 启动项目

开发模式下启动项目:

```bash
# 在项目根目录中
npm run start:dev
```

这会启动 NestJS 服务器，并将 Next.js 作为中间件集成。

## 构建与部署

构建项目:

```bash
# 在项目根目录中
npm run build
```

这将首先构建前端项目，然后构建后端项目。

生产模式下启动:

```bash
# 在项目根目录中
npm run start
```

## 目录结构说明

- `backend/src/next/` - Next.js 与 NestJS 的集成代码
- `frontend/pages/` - Next.js 页面组件

## API 文档

API 文档可以通过 Swagger UI 访问：http://localhost:3000/api/docs
