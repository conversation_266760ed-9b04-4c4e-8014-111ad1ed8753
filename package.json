{"name": "mini-shop", "version": "1.0.0", "description": "Mini Shop with NestJS and Next.js", "scripts": {"start": "cd backend && npm run start", "start:dev": "cd frontend && npm run compile-tailwind && cd ../backend && npm run start:dev", "dev:tailwindcss": "cd frontend && npm run dev:tailwindcss", "clean": "cd backend && npm run clean", "build": "cd backend && npm run build", "generate:api": "node script/generate-api-client.js", "generate:api:no-schemas": "node script/generate-api-client-no-schemas.js", "scan:dto": "node script/scan-types.js"}, "workspaces": ["backend", "frontend", "common"], "dependencies": {"next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@openapitools/openapi-generator-cli": "^2.19.1", "@types/js-yaml": "^4.0.9", "ajv": "^8.17.1", "glob": "^11.0.2", "js-yaml": "^4.1.0", "orval": "^7.8.0"}}