# API 客户端生成脚本

这个目录包含了用于生成 API 客户端的脚本文件。

## 脚本文件

- `scan-types.js`: 扫描 common/src 目录中的所有类型，并生成类型映射
- `generate-api-client.js`: 使用 Orval 生成基本的 API 客户端
- `generate-api-client-no-schemas.js`: 使用 Orval 生成 API 客户端，并使用项目中已有的类型，不生成重复的类型定义

## 使用方法

### 扫描类型

```bash
npm run scan:dto
```

这将扫描 common/src 目录中的所有类型，并生成类型映射。

### 生成 API 客户端

```bash
npm run generate:api
```

这将使用 Orval 生成基本的 API 客户端。

### 生成不包含重复类型定义的 API 客户端

```bash
npm run generate:api:no-schemas
```

这将使用 Orval 生成 API 客户端，并使用项目中已有的类型，不生成重复的类型定义。

## 工作原理

1. `scan-types.js` 扫描 common/src 目录中的所有类型，并生成类型映射
2. `generate-api-client-no-schemas.js` 使用 Orval 生成 API 客户端，然后：
   - 删除 schemas.ts 文件
   - 更新导入路径
   - 创建 types.ts 文件，导出所有需要的类型
   - 创建 index.ts 文件，提供友好的 API 接口

## 配置

配置文件位于项目根目录的 `orval.config.js` 文件中。
