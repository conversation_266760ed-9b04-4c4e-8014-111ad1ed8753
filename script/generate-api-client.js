const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..');
const OUTPUT_DIR = path.join(ROOT_DIR, 'common/api-client/generated');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * Generate API client using Orval
 */
function generateApiClient() {
  console.log('Generating API client using Orval...');
  
  try {
    // Generate API client
    execSync('node script/generate-api-client-no-schemas.js', { stdio: 'inherit', cwd: ROOT_DIR });
    console.log('API client generated successfully');
  } catch (error) {
    console.error('Error generating API client:', error);
    process.exit(1);
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('Starting API client generation...');
    
    // Generate API client
    generateApiClient();
    
    console.log('API client generation completed successfully');
  } catch (error) {
    console.error('Error generating API client:', error);
    process.exit(1);
  }
}

// Run main function
main();
