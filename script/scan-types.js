const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * 扫描 common/src 目录下的所有导出类型
 * @param {string} commonSrcDir - common/src 目录的路径
 * @returns {Object} 类型名称到文件路径的映射
 */
function scanTypes(commonSrcDir) {
  console.log(`[scan-types] 开始扫描 ${commonSrcDir} 目录...`);

  const typeMap = {};

  // 查找所有 .ts 文件，排除 api-client/generated 目录
  const files = glob.sync('**/*.ts', {
    cwd: commonSrcDir,
    nodir: true,
    ignore: ['**/api-client/generated/**']
  });
  console.log(`[scan-types] 找到 ${files.length} 个 .ts 文件`);

  files.forEach(file => {
    const fullPath = path.join(commonSrcDir, file);
    const content = fs.readFileSync(fullPath, 'utf-8');

    // 查找所有导出的类、接口、类型和枚举
    const exportMatches = content.matchAll(/export\s+(class|interface|type|enum)\s+(\w+)/g);

    for (const match of Array.from(exportMatches)) {
      const typeName = match[2];
      if (typeName) {
        // 存储类型名称和文件路径的映射
        const normalizedPath = file.replace(/\.ts$/, '').replace(/\\/g, '/');
        typeMap[typeName] = normalizedPath;
        console.log(`[scan-types] 找到类型: ${typeName} => ${normalizedPath}`);
      }
    }
  });

  return typeMap;
}

/**
 * 生成 Orval 配置中的 schemas 部分
 * @param {Object} typeMap - 类型名称到文件路径的映射
 * @returns {Object} Orval schemas 配置
 */
function generateSchemasConfig(typeMap) {
  const schemas = {};

  Object.entries(typeMap).forEach(([typeName, filePath]) => {
    schemas[typeName] = {
      type: 'import',
      from: `../${filePath}`,
      name: typeName,
    };
  });

  return schemas;
}

module.exports = {
  scanTypes,
  generateSchemasConfig,
};
