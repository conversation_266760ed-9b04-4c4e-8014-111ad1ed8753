const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 配置
const ROOT_DIR = path.resolve(__dirname, '..');
const OUTPUT_DIR = path.join(ROOT_DIR, 'common/src/api-client/generated');

// 确保输出目录存在
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// 确保 mutator 目录存在
const MUTATOR_DIR = path.join(ROOT_DIR, 'common/src/api-client/mutator');
if (!fs.existsSync(MUTATOR_DIR)) {
  fs.mkdirSync(MUTATOR_DIR, { recursive: true });
}

/**
 * 生成 API 客户端
 */
function generateApiClient() {
  console.log('Starting API client generation without schemas...');
  
  try {
    // 使用 Orval 生成 API 客户端
    console.log('Generating API client using Orval...');
    execSync('npx orval --config ./orval.config.js', { stdio: 'inherit', cwd: ROOT_DIR });
    console.log('API client generated successfully');

    // 检查并删除 schemas 文件
    const schemasFile = path.join(OUTPUT_DIR, 'miniShopAPI.schemas.ts');
    if (fs.existsSync(schemasFile)) {
      console.log(`Removing schemas file: ${schemasFile}`);
      fs.unlinkSync(schemasFile);
    }

    // 更新生成的文件中的导入语句
    updateImports();

    // 创建自定义的 types.ts 文件，导出所有需要的类型
    createTypesFile();

    // 创建 index.ts 文件，重新导出所有内容
    createIndexFile();

  } catch (error) {
    console.error('Error generating API client:', error);
    process.exit(1);
  }
}

/**
 * 更新生成的文件中的导入语句
 */
function updateImports() {
  console.log('Updating imports in generated files...');

  // 获取所有生成的 .ts 文件
  const files = getAllFiles(OUTPUT_DIR, '.ts');

  // 更新每个文件中的导入语句
  files.forEach(file => {
    // 跳过 index.ts 和 types.ts 文件
    if (file.endsWith('index.ts') || file.endsWith('types.ts')) {
      return;
    }

    let content = fs.readFileSync(file, 'utf-8');

    // 替换导入语句
    content = content.replace(
      /import\s+\{\s*AXIOS_INSTANCE\s*\}\s+from\s+['"]\.\.\/\.\.\/mutator\/custom-instance['"]/g,
      "import { AXIOS_INSTANCE } from '../mutator/custom-instance'"
    );

    // 保存更新后的文件
    fs.writeFileSync(file, content);
  });

  console.log('Imports updated successfully');
}

/**
 * 创建 types.ts 文件，导出所有需要的类型
 */
function createTypesFile() {
  console.log('Creating types file...');

  // 使用 scan-types.js 扫描所有类型
  const { scanTypes } = require('./scan-types');
  const commonSrcDir = path.join(ROOT_DIR, 'common/src');
  const typeMap = scanTypes(commonSrcDir);

  // 按文件路径分组类型
  const typesByFile = {};
  Object.entries(typeMap).forEach(([typeName, filePath]) => {
    // 跳过 api-client/generated 目录中的类型，避免循环引用
    if (filePath.includes('api-client/generated')) {
      return;
    }

    if (!typesByFile[filePath]) {
      typesByFile[filePath] = [];
    }
    typesByFile[filePath].push(typeName);
  });

  // 创建类型文件内容
  let typesContent = `// This file is auto-generated by generate-api-client-no-schemas.js
// It exports all types from common/src that are used in the API client

// Define types that are not in the project but are in the OpenAPI spec
export interface SendSmsDto {
  phone?: string;
  captcha?: string;
}

export interface VerifySmsDto {
  phone?: string;
  code?: string;
}

`;

  // 为每个文件创建导出语句
  Object.entries(typesByFile).forEach(([filePath, typeNames]) => {
    if (typeNames.length > 0) {
      // 在 api-client/generated 目录中，需要使用 '../../' 前缀来导入 user 目录中的文件
      typesContent += `export { ${typeNames.join(', ')} } from '../../${filePath}';\n`;
    }
  });

  // 自动创建类型映射
  const typeAliases = [];
  const inlineTypes = [];

  // 查找生成的文件中使用的类型
  const apiFiles = getAllFiles(OUTPUT_DIR, '.ts');
  apiFiles.forEach(file => {
    // 跳过 types.ts 文件
    if (file.endsWith('types.ts')) {
      return;
    }

    const content = fs.readFileSync(file, 'utf-8');
    
    // 查找所有从 "../types" 导入的类型
    const importMatches = content.match(/import\s+type\s+\{\s*([^}]+)\s*\}\s+from\s+["']\.\.\/types["']/g);
    if (importMatches) {
      importMatches.forEach(importMatch => {
        // 提取导入的类型名称
        const typeMatch = importMatch.match(/import\s+type\s+\{\s*([^}]+)\s*\}\s+from/);
        if (typeMatch && typeMatch[1]) {
          const importedTypes = typeMatch[1].split(',').map(t => t.trim());
          
          // 检查每个导入的类型
          importedTypes.forEach(importedType => {
            // 如果是控制器参数类型，则创建类型别名
            if (importedType.endsWith('Params') && !typeMap[importedType]) {
              // 尝试找到对应的 DTO 类型
              const controllerMatch = importedType.match(/([A-Za-z]+)Controller([A-Za-z]+)Params/);
              if (controllerMatch) {
                const entity = controllerMatch[1]; // 实体名称，如 User
                const action = controllerMatch[2]; // 操作名称，如 FindAll
                
                // 尝试不同的 DTO 命名模式
                const possibleDtoNames = [
                  `${entity}QueryDto`,                // UserQueryDto
                  `${entity}${action}Dto`,           // UserFindAllDto
                  `${entity}${action}QueryDto`,      // UserFindAllQueryDto
                  `${action}${entity}Dto`,           // FindAllUserDto
                  `${action}${entity}QueryDto`,      // FindAllUserQueryDto
                  `${entity}Dto`,                    // UserDto
                  `${entity}Params`                   // UserParams
                ];
                
                // 查找匹配的 DTO
                let foundDto = null;
                for (const dtoName of possibleDtoNames) {
                  if (typeMap[dtoName]) {
                    foundDto = dtoName;
                    break;
                  }
                }
                
                if (foundDto) {
                  // 找到匹配的 DTO，创建类型别名
                  typeAliases.push(`export type ${importedType} = ${foundDto};`);
                } else {
                  // 没有找到匹配的 DTO，创建内联类型
                  inlineTypes.push(`export interface ${importedType} {
  limit?: number;
  page?: number;
  sortOrder?: 'ASC' | 'DESC';
  sortBy?: string;
  status?: string;
  phone?: string;
  email?: string;
  username?: string;
}`);
                }
              }
            }
          });
        }
      });
    }
  });

  // 添加内联类型定义
  if (inlineTypes.length > 0) {
    typesContent += '// 内联类型定义\n';
    inlineTypes.forEach(typeDef => {
      typesContent += typeDef + '\n\n';
    });
  }
  
  // 添加类型别名
  if (typeAliases.length > 0) {
    typesContent += '// 为 OpenAPI 规范中的类型创建别名\n';
    typeAliases.forEach(alias => {
      typesContent += alias + '\n';
    });
  }

  // 保存类型文件
  fs.writeFileSync(path.join(OUTPUT_DIR, 'types.ts'), typesContent);
  console.log('Types file created successfully');
}

/**
 * 创建 index.ts 文件，重新导出所有内容
 */
function createIndexFile() {
  console.log('Creating index file...');

  try {
    // 获取所有 API 文件
    const apiFiles = fs.readdirSync(OUTPUT_DIR)
      .filter(file => fs.statSync(path.join(OUTPUT_DIR, file)).isDirectory())
      .map(dir => ({
        name: dir,
        path: `./${dir}/${dir}`
      }));

    // 创建 index.ts 文件内容
    let indexContent = `// This file is auto-generated by generate-api-client-no-schemas.js
// It re-exports all API clients and types

// Import and export axios instance and custom instance
import { AXIOS_INSTANCE, customInstance } from '../../../mutator/custom-instance';
export { AXIOS_INSTANCE, customInstance };

// Import and export all types
import * as ApiTypes from './types';
export * from './types';

// Import API modules
`;

    // 导入 API 模块
    apiFiles.forEach(api => {
      indexContent += `import { get${api.name.charAt(0).toUpperCase() + api.name.slice(1)} } from '${api.path}';\n`;
    });

    indexContent += `
// Create API classes
`;

    // 创建 API 类
    apiFiles.forEach(api => {
      const className = `${api.name.charAt(0).toUpperCase() + api.name.slice(1)}Api`;
      const apiFunctionName = `get${api.name.charAt(0).toUpperCase() + api.name.slice(1)}`;
      
      indexContent += `export class ${className} {
  private client: typeof customInstance;

  constructor(client = customInstance) {
    this.client = client;
  }

  private api = ${apiFunctionName}();

`;

      // 读取 API 文件，提取方法
      const apiFilePath = path.join(OUTPUT_DIR, api.name, `${api.name}.ts`);
      if (fs.existsSync(apiFilePath)) {
        const apiFileContent = fs.readFileSync(apiFilePath, 'utf-8');
        
        // 提取所有控制器方法
        const methodRegex = /const\s+([a-zA-Z0-9_]+)\s*=\s*\(([^)]*)\)\s*=>\s*\{[\s\S]*?return[\s\S]*?;\s*\};/g;
        let match;
        
        while ((match = methodRegex.exec(apiFileContent)) !== null) {
          const controllerMethodName = match[1];
          const paramsStr = match[2];
          
          // 生成方法名称（去掉控制器前缀）
          let methodName = controllerMethodName;
          const controllerPrefix = `${api.name}Controller`;
          if (methodName.startsWith(controllerPrefix)) {
            methodName = methodName.substring(controllerPrefix.length);
            // 首字母小写
            methodName = methodName.charAt(0).toLowerCase() + methodName.slice(1);
          }
          
          // 处理参数
          let methodParams = '';
          let methodParamsWithTypes = '';
          
          if (paramsStr && paramsStr.trim()) {
            const params = paramsStr.trim().split(',').map(p => p.trim());
            
            methodParams = params.map(param => {
              const [paramName] = param.split(':').map(p => p.trim());
              return paramName;
            }).join(', ');
            
            methodParamsWithTypes = params.map(param => {
              let [paramName, paramType] = param.split(':').map(p => p.trim());
              
              // 如果参数类型包含 Dto 或 Params，使用 ApiTypes 命名空间
              if (paramType && (paramType.includes('Dto') || paramType.includes('Params'))) {
                paramType = `ApiTypes.${paramType}`;
              }
              
              return paramName + (paramType ? `: ${paramType}` : '');
            }).join(', ');
          }
          
          // 生成方法注释
          const summaryMatch = match[0].match(/@summary\s+([^\n]+)/);
          const summary = summaryMatch ? summaryMatch[1].trim() : `${methodName} operation`;
          
          // 生成方法代码
          indexContent += `  /**
   * ${summary}
   */
  ${methodName}(${methodParamsWithTypes}) {
    return this.api.${controllerMethodName}(${methodParams});
  }

`;
        }
      }
      
      indexContent += `}

`;
    });

    indexContent += `// Export API client instances
export const apiClients = {
`;

    // 导出 API 客户端实例
    apiFiles.forEach(api => {
      const className = `${api.name.charAt(0).toUpperCase() + api.name.slice(1)}Api`;
      const instanceName = `${api.name}Api`;
      
      indexContent += `  ${api.name}: new ${className}(),
`;
    });

    indexContent += `};
`;

    // 保存 index.ts 文件
    fs.writeFileSync(path.join(OUTPUT_DIR, 'index.ts'), indexContent);
    console.log('Index file created successfully');
  } catch (error) {
    console.error('Error creating index file:', error);
    throw error;
  }
}

/**
 * 获取目录中的所有文件
 * @param {string} dir 目录路径
 * @param {string} extension 文件扩展名
 * @returns {string[]} 文件路径数组
 */
function getAllFiles(dir, extension) {
  let results = [];
  
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // 递归获取子目录中的文件
      results = results.concat(getAllFiles(filePath, extension));
    } else if (file.endsWith(extension)) {
      // 添加匹配的文件
      results.push(filePath);
    }
  });
  
  return results;
}

/**
 * 主函数
 */
function main() {
  generateApiClient();
}

// 执行主函数
main();
