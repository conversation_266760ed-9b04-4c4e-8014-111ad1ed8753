{"openapi": "3.0.0", "paths": {"/api/users": {"get": {"operationId": "UserController_findAll", "summary": "Query users with filters and pagination", "parameters": [{"name": "username", "required": false, "in": "query", "description": "Filter by username", "schema": {"type": "string"}}, {"name": "email", "required": false, "in": "query", "description": "Filter by email", "schema": {"type": "string"}}, {"name": "phone", "required": false, "in": "query", "description": "Filter by phone number", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "Filter by user status", "schema": {"enum": ["active", "inactive", "banned"], "type": "string"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Field to sort by", "schema": {"type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order (ascending or descending)", "schema": {"enum": ["ASC", "DESC"], "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number (1-based)", "schema": {"type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of items per page", "schema": {"type": "number"}}], "responses": {"200": {"description": "Returns a list of users with pagination metadata"}}, "tags": ["users"]}}, "/api/users/{id}": {"get": {"operationId": "UserController_findOne", "summary": "Get a user by ID", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the user"}, "404": {"description": "User not found"}}, "tags": ["users"]}}, "/api/auth/login": {"post": {"operationId": "AuthController_login", "summary": "Login with credentials", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"201": {"description": "User logged in successfully"}, "401": {"description": "Unauthorized"}}, "tags": ["auth"]}}, "/api/auth/refresh-token": {"post": {"operationId": "AuthController_refreshToken", "summary": "Refresh access token", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON> refreshed successfully"}, "401": {"description": "Unauthorized"}}, "tags": ["auth"]}}, "/api/auth/me": {"get": {"operationId": "AuthController_getCurrentUser", "summary": "Get current user information", "parameters": [], "responses": {"200": {"description": "Returns the current user information"}, "401": {"description": "Unauthorized"}}, "tags": ["auth"], "security": [{"bearer": []}]}}, "/api/sms/captcha": {"get": {"operationId": "SmsController_getCaptcha", "summary": "获取图形验证码", "parameters": [], "responses": {"200": {"description": "验证码生成成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseDto"}}}}}, "tags": ["sms"]}}, "/api/sms/send": {"post": {"operationId": "SmsController_sendSmsCode", "summary": "发送短信验证码", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendSmsDto"}}}}, "responses": {"200": {"description": "验证码发送成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsResponseDto"}}}}, "400": {"description": "验证码发送失败"}}, "tags": ["sms"]}}, "/api/sms/verify": {"post": {"operationId": "SmsController_verifySmsCode", "summary": "验证短信验证码", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifySmsDto"}}}}, "responses": {"200": {"description": "验证码验证成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsResponseDto"}}}}, "400": {"description": "验证码验证失败"}}, "tags": ["sms"]}}, "/api/menus": {"get": {"operationId": "MenuController_findAll", "summary": "Get all menus", "parameters": [], "responses": {"200": {"description": "Return all menus"}}, "tags": ["menus"]}, "post": {"operationId": "MenuController_create", "summary": "Create a new menu", "parameters": [], "responses": {"201": {"description": "The menu has been successfully created"}}, "tags": ["menus"]}}, "/api/menus/tree": {"get": {"operationId": "MenuController_getMenuTree", "summary": "Get menu tree", "parameters": [], "responses": {"200": {"description": "Return menu tree"}}, "tags": ["menus"]}}, "/api/menus/{id}": {"get": {"operationId": "MenuController_findOne", "summary": "Get menu by id", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Return menu by id"}, "404": {"description": "<PERSON><PERSON> not found"}}, "tags": ["menus"]}, "put": {"operationId": "MenuController_update", "summary": "Update a menu", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "The menu has been successfully updated"}, "404": {"description": "<PERSON><PERSON> not found"}}, "tags": ["menus"]}, "delete": {"operationId": "MenuController_remove", "summary": "Delete a menu", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "The menu has been successfully deleted"}, "404": {"description": "<PERSON><PERSON> not found"}}, "tags": ["menus"]}}, "/api/departments": {"get": {"operationId": "DepartmentController_findAll", "summary": "Get all departments with optional filtering", "parameters": [], "responses": {"200": {"description": "Return all departments"}}, "tags": ["departments"]}, "post": {"operationId": "DepartmentController_create", "summary": "Create a new department", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDepartmentDto"}}}}, "responses": {"201": {"description": "The department has been successfully created"}}, "tags": ["departments"]}}, "/api/departments/tree": {"get": {"operationId": "DepartmentController_getDepartmentTree", "summary": "Get department tree", "parameters": [], "responses": {"200": {"description": "Return department tree"}}, "tags": ["departments"]}}, "/api/departments/{id}": {"get": {"operationId": "DepartmentController_findOne", "summary": "Get department by id", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Return department by id"}, "404": {"description": "Department not found"}}, "tags": ["departments"]}, "put": {"operationId": "DepartmentController_update", "summary": "Update a department", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentDto"}}}}, "responses": {"200": {"description": "The department has been successfully updated"}, "404": {"description": "Department not found"}}, "tags": ["departments"]}, "delete": {"operationId": "DepartmentController_remove", "summary": "Delete a department", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "The department has been successfully deleted"}, "404": {"description": "Department not found"}}, "tags": ["departments"]}}, "/api/dictionary": {"get": {"operationId": "DictionaryController_findAll", "summary": "Get all dictionary entries with optional filtering", "parameters": [], "responses": {"200": {"description": "Return all dictionary entries"}}, "tags": ["dictionary"]}, "post": {"operationId": "DictionaryController_create", "summary": "Create a new dictionary entry", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDictionaryEntryDto"}}}}, "responses": {"201": {"description": "The dictionary entry has been successfully created"}}, "tags": ["dictionary"]}}, "/api/dictionary/{id}": {"get": {"operationId": "DictionaryController_findOne", "summary": "Get dictionary entry by id", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "Return dictionary entry by id"}, "404": {"description": "Dictionary entry not found"}}, "tags": ["dictionary"]}, "put": {"operationId": "DictionaryController_update", "summary": "Update a dictionary entry", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDictionaryEntryDto"}}}}, "responses": {"200": {"description": "The dictionary entry has been successfully updated"}, "404": {"description": "Dictionary entry not found"}}, "tags": ["dictionary"]}, "delete": {"operationId": "DictionaryController_remove", "summary": "Delete a dictionary entry", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "The dictionary entry has been successfully deleted"}, "404": {"description": "Dictionary entry not found"}}, "tags": ["dictionary"]}}, "/api/dictionary/key/{key}": {"get": {"operationId": "DictionaryController_findBy<PERSON>ey", "summary": "Get dictionary entry by key", "parameters": [{"name": "key", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return dictionary entry by key"}, "404": {"description": "Dictionary entry not found"}}, "tags": ["dictionary"]}}, "/api/dictionary/{id}/values": {"post": {"operationId": "DictionaryController_addValue", "summary": "Add a value to a dictionary entry", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"201": {"description": "The value has been successfully added"}, "404": {"description": "Dictionary entry not found"}}, "tags": ["dictionary"]}}, "/api/dictionary/values/{id}": {"delete": {"operationId": "DictionaryController_removeValue", "summary": "Remove a value from a dictionary entry", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "The value has been successfully removed"}, "404": {"description": "Dictionary value not found"}}, "tags": ["dictionary"]}}}, "info": {"title": "Mini Shop API", "description": "The Mini Shop API documentation", "version": "1.0", "contact": {}}, "tags": [{"name": "users", "description": ""}, {"name": "auth", "description": ""}, {"name": "sms", "description": ""}, {"name": "menus", "description": ""}, {"name": "departments", "description": ""}, {"name": "dictionary", "description": ""}], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"LoginDto": {"type": "object", "properties": {}}, "RefreshTokenDto": {"type": "object", "properties": {}}, "ResponseDto": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": true}, "message": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"type": "object", "description": "响应数据"}}, "required": ["success"]}, "SendSmsDto": {"type": "object", "properties": {"phone": {"type": "string", "description": "手机号", "example": "13800138000"}, "captchaToken": {"type": "string", "description": "图形验证码token", "example": "a1b2c3d4"}, "captchaText": {"type": "string", "description": "图形验证码文本", "example": "a1b2"}}, "required": ["phone", "captchaToken", "captchaText"]}, "SmsResponseDto": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": true}, "message": {"type": "string", "description": "响应消息", "example": "验证码发送成功"}}, "required": ["success", "message"]}, "VerifySmsDto": {"type": "object", "properties": {"phone": {"type": "string", "description": "手机号", "example": "13800138000"}, "code": {"type": "string", "description": "短信验证码", "example": "123456"}}, "required": ["phone", "code"]}, "CreateDepartmentDto": {"type": "object", "properties": {}}, "UpdateDepartmentDto": {"type": "object", "properties": {}}, "CreateDictionaryEntryDto": {"type": "object", "properties": {}}, "UpdateDictionaryEntryDto": {"type": "object", "properties": {}}}}}