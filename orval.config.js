const path = require('path');
const { scanTypes, generateSchemasConfig } = require('./script/scan-types');

// 扫描 common/src 目录下的所有类型
const commonSrcDir = path.resolve(__dirname, 'common/src');
const typeMap = scanTypes(commonSrcDir);

// 为每个类型创建导入路径映射
const typeImportMap = {};
Object.entries(typeMap).forEach(([typeName, filePath]) => {
  typeImportMap[typeName] = `../${filePath}`;
});

// 生成 schemas 配置
const schemasConfig = generateSchemasConfig(typeMap);

module.exports = {
  'mini-shop-api': {
    input: { target: './openapi.json' },
    output: {
      mode: 'tags-split',
      target: './common/src/api-client/generated',
      client: 'axios',
      prettier: true,
      override: {
        mutator: {
          path: './common/src/api-client/mutator/custom-instance.ts',
          name: 'customInstance',
        },
        transformer: (schema, options) => {
          // 如果 schema 有标题且在我们的类型映射中，则替换为导入
          if (schema.title && typeImportMap[schema.title]) {
            return {
              type: 'import',
              from: typeImportMap[schema.title],
              name: schema.title,
            };
          }

          // 否则返回原始 schema
          return schema;
        },
        components: {
          schemas: schemasConfig,
        },
        mock: false,
        query: {
          useQuery: true,
          useInfinite: true,
          useInfiniteQueryParam: 'page',
        },
      },
    },
  },
};