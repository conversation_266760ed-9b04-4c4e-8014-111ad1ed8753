/*! tailwindcss v4.1.7 | MIT License | https://tailwindcss.com */ @layer properties;:root,:host{--color-border:var(--border)}body{overscroll-behavior:none;background-color:transparent}:root{--font-sans:var(--font-inter);--header-height:calc(var(--spacing) * 12 + 1px)}.theme-scaled{@media (min-width:1024px){--radius:0.6rem;--text-lg:1.05rem;--text-base:0.85rem;--text-sm:0.8rem;--spacing:0.222222rem}[data-slot='card']{--spacing:0.16rem}[data-slot='select-trigger'],[data-slot='toggle-group-item']{--spacing:0.222222rem}}.theme-default,.theme-default-scaled{--primary:var(--color-neutral-600);--primary-foreground:var(--color-neutral-50);&:is(.dark *){--primary:var(--color-neutral-500);--primary-foreground:var(--color-neutral-50)}}.theme-blue,.theme-blue-scaled{--primary:var(--color-blue-600);--primary-foreground:var(--color-blue-50);&:is(.dark *){--primary:var(--color-blue-500);--primary-foreground:var(--color-blue-50)}}.theme-green,.theme-green-scaled{--primary:var(--color-lime-600);--primary-foreground:var(--color-lime-50);&:is(.dark *){--primary:var(--color-lime-600);--primary-foreground:var(--color-lime-50)}}.theme-amber,.theme-amber-scaled{--primary:var(--color-amber-600);--primary-foreground:var(--color-amber-50);&:is(.dark *){--primary:var(--color-amber-500);--primary-foreground:var(--color-amber-50)}}.theme-mono,.theme-mono-scaled{--font-sans:var(--font-mono);--primary:var(--color-neutral-600);--primary-foreground:var(--color-neutral-50);&:is(.dark *){--primary:var(--color-neutral-500);--primary-foreground:var(--color-neutral-50)}.rounded-xs,.rounded-sm,.rounded-md,.rounded-lg,.rounded-xl{border-radius:0 !important;border-radius:0}.shadow-xs,.shadow-sm,.shadow-md,.shadow-lg,.shadow-xl{--tw-shadow:0 0 #0000 !important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow) !important}[data-slot='toggle-group'],[data-slot='toggle-group-item']{border-radius:0 !important;--tw-shadow:0 0 #0000 !important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow) !important}}.\@container\/card{container-type:inline-size;container-name:card}.\@container\/card-header{container-type:inline-size;container-name:card-header}.pointer-events-none{pointer-events:none}.invisible{visibility:hidden}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.sticky{position:sticky}.top-1\/2{top:calc(1/2 * 100%)}.top-\[0\.3rem\]{top:0.3rem}.top-\[1px\]{top:1px}.top-\[50\%\]{top:50%}.top-\[60\%\]{top:60%}.top-full{top:100%}.right-\[0\.3rem\]{right:0.3rem}.left-1\/2{left:calc(1/2 * 100%)}.left-\[50\%\]{left:50%}.isolate{isolation:isolate}.z-10{z-index:10}.z-20{z-index:20}.z-50{z-index:50}.z-99999{z-index:99999}.z-\[-1\]\!{z-index:-1 !important}.z-\[1\]{z-index:1}.col-span-4{grid-column:span 4 / span 4}.col-start-2{grid-column-start:2}.row-span-2{grid-row:span 2 / span 2}.row-start-1{grid-row-start:1}.container{width:100%}.mx-auto{margin-inline:auto}.mt-auto{margin-top:auto}.mr-auto{margin-right:auto}.ml-auto{margin-left:auto}.line-clamp-1{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline-flex{display:inline-flex}.table{display:table}.table-caption{display:table-caption}.table-cell{display:table-cell}.table-row{display:table-row}.field-sizing-content{field-sizing:content}.aspect-auto{aspect-ratio:auto}.aspect-square{aspect-ratio:1 / 1}.size-full{width:100%;height:100%}.h-\[1\.15rem\]{height:1.15rem}.h-\[1px\]{height:1px}.h-\[75vh\]{height:75vh}.h-\[250px\]{height:250px}.h-\[280px\]{height:280px}.h-\[300px\]{height:300px}.h-\[316px\]{height:316px}.h-\[calc\(100\%-1px\)\]{height:calc(100% - 1px)}.h-\[calc\(100dvh-52px\)\]{height:calc(100dvh - 52px)}.h-\[var\(--radix-navigation-menu-viewport-height\)\]{height:var(--radix-navigation-menu-viewport-height)}.h-\[var\(--radix-select-trigger-height\)\]{height:var(--radix-select-trigger-height)}.h-auto{height:auto}.h-fit{height:-moz-fit-content;height:fit-content}.h-full{height:100%}.h-px{height:1px}.h-screen{height:100vh}.h-svh{height:100svh}.max-h-\(--radix-context-menu-content-available-height\){max-height:var(--radix-context-menu-content-available-height)}.max-h-\(--radix-dropdown-menu-content-available-height\){max-height:var(--radix-dropdown-menu-content-available-height)}.max-h-\(--radix-select-content-available-height\){max-height:var(--radix-select-content-available-height)}.max-h-\[18\.75rem\]{max-height:18.75rem}.max-h-\[75vh\]{max-height:75vh}.max-h-\[300px\]{max-height:300px}.max-h-\[400px\]{max-height:400px}.max-h-full{max-height:100%}.min-h-svh{min-height:100svh}.w-\(--radix-dropdown-menu-trigger-width\){width:var(--radix-dropdown-menu-trigger-width)}.w-\(--sidebar-width\){width:var(--sidebar-width)}.w-3\/4{width:calc(3/4 * 100%)}.w-\[--radix-dropdown-menu-trigger-width\]{width:--radix-dropdown-menu-trigger-width}.w-\[1px\]{width:1px}.w-\[4\.5rem\]{width:4.5rem}.w-\[12\.5rem\]{width:12.5rem}.w-\[80px\]{width:80px}.w-\[100px\]{width:100px}.w-\[120px\]{width:120px}.w-\[140px\]{width:140px}.w-\[150px\]{width:150px}.w-\[160px\]{width:160px}.w-\[180px\]{width:180px}.w-\[250px\]{width:250px}.w-\[300px\]{width:300px}.w-\[350px\]{width:350px}.w-auto{width:auto}.w-fit{width:-moz-fit-content;width:fit-content}.w-full{width:100%}.w-max{width:-moz-max-content;width:max-content}.w-px{width:1px}.max-w-\(--skeleton-width\){max-width:var(--skeleton-width)}.max-w-\[600px\]{max-width:600px}.max-w-\[calc\(100\%-2rem\)\]{max-width:calc(100% - 2rem)}.max-w-full{max-width:100%}.max-w-max{max-width:-moz-max-content;max-width:max-content}.min-w-\[8rem\]{min-width:8rem}.min-w-\[12rem\]{min-width:12rem}.min-w-\[120px\]{min-width:120px}.min-w-\[var\(--radix-select-trigger-width\)\]{min-width:var(--radix-select-trigger-width)}.flex-1{flex:1}.flex-shrink-0{flex-shrink:0}.shrink-0{flex-shrink:0}.grow{flex-grow:1}.caption-bottom{caption-side:bottom}.border-collapse{border-collapse:collapse}.origin-\(--radix-context-menu-content-transform-origin\){transform-origin:var(--radix-context-menu-content-transform-origin)}.origin-\(--radix-dropdown-menu-content-transform-origin\){transform-origin:var(--radix-dropdown-menu-content-transform-origin)}.origin-\(--radix-hover-card-content-transform-origin\){transform-origin:var(--radix-hover-card-content-transform-origin)}.origin-\(--radix-menubar-content-transform-origin\){transform-origin:var(--radix-menubar-content-transform-origin)}.origin-\(--radix-popover-content-transform-origin\){transform-origin:var(--radix-popover-content-transform-origin)}.origin-\(--radix-select-content-transform-origin\){transform-origin:var(--radix-select-content-transform-origin)}.origin-\(--radix-tooltip-content-transform-origin\){transform-origin:var(--radix-tooltip-content-transform-origin)}.-translate-x-1\/2{--tw-translate-x:calc(calc(1/2 * 100%) * -1);translate:var(--tw-translate-x) var(--tw-translate-y)}.-translate-x-px{--tw-translate-x:-1px;translate:var(--tw-translate-x) var(--tw-translate-y)}.translate-x-\[-50\%\]{--tw-translate-x:-50%;translate:var(--tw-translate-x) var(--tw-translate-y)}.translate-x-px{--tw-translate-x:1px;translate:var(--tw-translate-x) var(--tw-translate-y)}.-translate-y-1\/2{--tw-translate-y:calc(calc(1/2 * 100%) * -1);translate:var(--tw-translate-x) var(--tw-translate-y)}.translate-y-\[-50\%\]{--tw-translate-y:-50%;translate:var(--tw-translate-x) var(--tw-translate-y)}.translate-y-\[calc\(-50\%_-_2px\)\]{--tw-translate-y:calc(-50% - 2px);translate:var(--tw-translate-x) var(--tw-translate-y)}.rotate-45{rotate:45deg}.transform{transform:var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,)}.animate-caret-blink{animation:caret-blink 1.25s ease-out infinite}.animate-in{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)}.cursor-default{cursor:default}.cursor-grab{cursor:grab}.cursor-pointer{cursor:pointer}.touch-none{touch-action:none}.resize-none{resize:none}.snap-none{scroll-snap-type:none}.snap-center{scroll-snap-align:center}.list-none{list-style-type:none}.grid-flow-col{grid-auto-flow:column}.auto-rows-min{grid-auto-rows:min-content}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.grid-cols-\[0_1fr\]{grid-template-columns:0 1fr}.grid-rows-\[auto_auto\]{grid-template-rows:auto auto}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-row{flex-direction:row}.flex-wrap{flex-wrap:wrap}.place-items-center{place-items:center}.items-center{align-items:center}.items-end{align-items:flex-end}.items-start{align-items:flex-start}.items-stretch{align-items:stretch}.justify-around{justify-content:space-around}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.justify-start{justify-content:flex-start}.justify-items-start{justify-items:start}.space-y-px{:where(& >:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(1px * var(--tw-space-y-reverse));margin-block-end:calc(1px * calc(1 - var(--tw-space-y-reverse)))}}.self-start{align-self:flex-start}.justify-self-end{justify-self:flex-end}.truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.overflow-x-hidden{overflow-x:hidden}.overflow-y-auto{overflow-y:auto}.overscroll-none{overscroll-behavior:none}.rounded-\[0\.5rem\]{border-radius:0.5rem}.rounded-\[2px\]{border-radius:2px}.rounded-\[4px\]{border-radius:4px}.rounded-\[inherit\]{border-radius:inherit}.rounded-full{border-radius:calc(infinity * 1px)}.rounded-lg{border-radius:var(--radius)}.rounded-md{border-radius:calc(var(--radius) - 2px)}.rounded-none{border-radius:0}.rounded-sm{border-radius:calc(var(--radius) - 4px)}.rounded-xl{border-radius:calc(var(--radius) + 4px)}.rounded-tl-sm{border-top-left-radius:calc(var(--radius) - 4px)}.rounded-r-md{border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}.border{border-style:var(--tw-border-style);border-width:1px}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-\[1\.5px\]{border-style:var(--tw-border-style);border-width:1.5px}.border-y{border-block-style:var(--tw-border-style);border-block-width:1px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-r{border-right-style:var(--tw-border-style);border-right-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.border-l-4{border-left-style:var(--tw-border-style);border-left-width:4px}.border-dashed{--tw-border-style:dashed;border-style:dashed}.border-none{--tw-border-style:none;border-style:none}.border-\(--color-border\){border-color:var(--color-border)}.border-border{border-color:var(--border)}.border-border\/50{border-color:var(--border);@supports (color:color-mix(in lab,red,red)){border-color:color-mix(in oklab,var(--border) 50%,transparent)}}.border-input{border-color:var(--input)}.border-muted-foreground\/25{border-color:var(--muted-foreground);@supports (color:color-mix(in lab,red,red)){border-color:color-mix(in oklab,var(--muted-foreground) 25%,transparent)}}.border-muted-foreground\/50{border-color:var(--muted-foreground);@supports (color:color-mix(in lab,red,red)){border-color:color-mix(in oklab,var(--muted-foreground) 50%,transparent)}}.border-primary{border-color:var(--primary)}.border-secondary{border-color:var(--secondary)}.border-sidebar-border{border-color:var(--sidebar-border)}.border-transparent{border-color:transparent}.border-t-transparent{border-top-color:transparent}.border-l-transparent{border-left-color:transparent}.bg-\(--color-bg\){background-color:var(--color-bg)}.bg-accent{background-color:var(--accent)}.bg-accent\/50{background-color:var(--accent);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--accent) 50%,transparent)}}.bg-background{background-color:var(--background)}.bg-background\/80{background-color:var(--background);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--background) 80%,transparent)}}.bg-border{background-color:var(--border)}.bg-card{background-color:var(--card)}.bg-destructive{background-color:var(--destructive)}.bg-foreground{background-color:var(--foreground)}.bg-muted{background-color:var(--muted)}.bg-muted\/50{background-color:var(--muted);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--muted) 50%,transparent)}}.bg-popover{background-color:var(--popover)}.bg-primary{background-color:var(--primary)}.bg-primary\/20{background-color:var(--primary);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--primary) 20%,transparent)}}.bg-secondary{background-color:var(--secondary)}.bg-sidebar{background-color:var(--sidebar)}.bg-sidebar-border{background-color:var(--sidebar-border)}.bg-transparent{background-color:transparent}.bg-linear-to-b{--tw-gradient-position:to bottom;@supports (background-image:linear-gradient(in lab,red,red)){--tw-gradient-position:to bottom in oklab}background-image:linear-gradient(var(--tw-gradient-stops))}.bg-linear-to-t{--tw-gradient-position:to top;@supports (background-image:linear-gradient(in lab,red,red)){--tw-gradient-position:to top in oklab}background-image:linear-gradient(var(--tw-gradient-stops))}.from-foreground{--tw-gradient-from:var(--foreground);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from) var(--tw-gradient-from-position),var(--tw-gradient-to) var(--tw-gradient-to-position))}.from-primary\/5{--tw-gradient-from:var(--primary);@supports (color:color-mix(in lab,red,red)){--tw-gradient-from:color-mix(in oklab,var(--primary) 5%,transparent)}--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from) var(--tw-gradient-from-position),var(--tw-gradient-to) var(--tw-gradient-to-position))}.to-primary\/20{--tw-gradient-to:var(--primary);@supports (color:color-mix(in lab,red,red)){--tw-gradient-to:color-mix(in oklab,var(--primary) 20%,transparent)}--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from) var(--tw-gradient-from-position),var(--tw-gradient-to) var(--tw-gradient-to-position))}.to-transparent{--tw-gradient-to:transparent;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from) var(--tw-gradient-from-position),var(--tw-gradient-to) var(--tw-gradient-to-position))}.bg-clip-text{-webkit-background-clip:text;background-clip:text}.fill-current{fill:currentcolor}.fill-foreground{fill:var(--foreground)}.fill-muted-foreground{fill:var(--muted-foreground)}.fill-primary{fill:var(--primary)}.object-cover{-o-object-fit:cover;object-fit:cover}.p-\[3px\]{padding:3px}.p-px{padding:1px}.text-center{text-align:center}.text-left{text-align:left}.align-middle{vertical-align:middle}.text-\[0\.8rem\]{font-size:0.8rem}.text-\[10px\]{font-size:10px}.text-\[10rem\]{font-size:10rem}.leading-none{--tw-leading:1;line-height:1}.text-balance{text-wrap:balance}.break-words{overflow-wrap:break-word}.whitespace-nowrap{white-space:nowrap}.whitespace-pre-wrap{white-space:pre-wrap}.text-accent-foreground{color:var(--accent-foreground)}.text-card-foreground{color:var(--card-foreground)}.text-current{color:currentcolor}.text-destructive{color:var(--destructive)}.text-foreground{color:var(--foreground)}.text-foreground\/80{color:var(--foreground);@supports (color:color-mix(in lab,red,red)){color:color-mix(in oklab,var(--foreground) 80%,transparent)}}.text-muted-foreground{color:var(--muted-foreground)}.text-muted-foreground\/70{color:var(--muted-foreground);@supports (color:color-mix(in lab,red,red)){color:color-mix(in oklab,var(--muted-foreground) 70%,transparent)}}.text-popover-foreground{color:var(--popover-foreground)}.text-primary{color:var(--primary)}.text-primary-foreground{color:var(--primary-foreground)}.text-primary\/50{color:var(--primary);@supports (color:color-mix(in lab,red,red)){color:color-mix(in oklab,var(--primary) 50%,transparent)}}.text-secondary-foreground{color:var(--secondary-foreground)}.text-secondary-foreground\/50{color:var(--secondary-foreground);@supports (color:color-mix(in lab,red,red)){color:color-mix(in oklab,var(--secondary-foreground) 50%,transparent)}}.text-sidebar-foreground{color:var(--sidebar-foreground)}.text-sidebar-foreground\/70{color:var(--sidebar-foreground);@supports (color:color-mix(in lab,red,red)){color:color-mix(in oklab,var(--sidebar-foreground) 70%,transparent)}}.text-sidebar-primary-foreground{color:var(--sidebar-primary-foreground)}.text-transparent{color:transparent}.capitalize{text-transform:capitalize}.uppercase{text-transform:uppercase}.tabular-nums{--tw-numeric-spacing:tabular-nums;font-variant-numeric:var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)}.no-underline\!{text-decoration-line:none !important}.underline{text-decoration-line:underline}.underline-offset-4{text-underline-offset:4px}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.opacity-0{opacity:0%}.opacity-30{opacity:30%}.opacity-50{opacity:50%}.opacity-60{opacity:60%}.opacity-70{opacity:70%}.opacity-100{opacity:100%}.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\]{--tw-shadow:0 0 0 1px var(--tw-shadow-color,hsl(var(--sidebar-border)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-none{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-0{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-1{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-2{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-primary{--tw-ring-color:var(--primary)}.ring-ring\/50{--tw-ring-color:var(--ring);@supports (color:color-mix(in lab,red,red)){--tw-ring-color:color-mix(in oklab,var(--ring) 50%,transparent)}}.ring-sidebar-ring{--tw-ring-color:var(--sidebar-ring)}.ring-offset-background{--tw-ring-offset-color:var(--background)}.outline-hidden{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){outline:2px solid transparent;outline-offset:2px}}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.filter{filter:var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-\[color\,box-shadow\]{transition-property:color,box-shadow;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-\[left\,right\,width\]{transition-property:left,right,width;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-\[margin\,opacity\]{transition-property:margin,opacity;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-\[width\,height\,padding\]{transition-property:width,height,padding;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-\[width\,height\]{transition-property:width,height;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-\[width\]{transition-property:width;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-shadow{transition-property:box-shadow;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-none{transition-property:none}.duration-200{--tw-duration:200ms;transition-duration:200ms}.duration-300{--tw-duration:300ms;transition-duration:300ms}.duration-1000{--tw-duration:1000ms;transition-duration:1000ms}.ease-linear{--tw-ease:linear;transition-timing-function:linear}.fade-in-0{--tw-enter-opacity:calc(0/100);--tw-enter-opacity:0}.outline-none{--tw-outline-style:none;outline-style:none}.select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.zoom-in-95{--tw-enter-scale:calc(95*1%);--tw-enter-scale:.95}.ring-inset{--tw-ring-inset:inset}.group-focus-within\/menu-item\:opacity-100{&:is(:where(.group\/menu-item):focus-within *){opacity:100%}}.group-hover\/menu-item\:opacity-100{&:is(:where(.group\/menu-item):hover *){@media (hover:hover){opacity:100%}}}.group-data-\[collapsible\=icon\]\:hidden{&:is(:where(.group)[data-collapsible="icon"] *){display:none}}.group-data-\[collapsible\=icon\]\:w-\(--sidebar-width-icon\){&:is(:where(.group)[data-collapsible="icon"] *){width:var(--sidebar-width-icon)}}.group-data-\[collapsible\=icon\]\:overflow-hidden{&:is(:where(.group)[data-collapsible="icon"] *){overflow:hidden}}.group-data-\[collapsible\=icon\]\:opacity-0{&:is(:where(.group)[data-collapsible="icon"] *){opacity:0%}}.group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]{&:is(:where(.group)[data-collapsible="offcanvas"] *){right:calc(var(--sidebar-width) * -1)}}.group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]{&:is(:where(.group)[data-collapsible="offcanvas"] *){left:calc(var(--sidebar-width) * -1)}}.group-data-\[disabled\=true\]\:pointer-events-none{&:is(:where(.group)[data-disabled="true"] *){pointer-events:none}}.group-data-\[disabled\=true\]\:opacity-50{&:is(:where(.group)[data-disabled="true"] *){opacity:50%}}.group-data-\[side\=left\]\:border-r{&:is(:where(.group)[data-side="left"] *){border-right-style:var(--tw-border-style);border-right-width:1px}}.group-data-\[side\=right\]\:rotate-180{&:is(:where(.group)[data-side="right"] *){rotate:180deg}}.group-data-\[side\=right\]\:border-l{&:is(:where(.group)[data-side="right"] *){border-left-style:var(--tw-border-style);border-left-width:1px}}.group-data-\[state\=open\]\:rotate-180{&:is(:where(.group)[data-state="open"] *){rotate:180deg}}.group-data-\[state\=open\]\/collapsible\:rotate-90{&:is(:where(.group\/collapsible)[data-state="open"] *){rotate:90deg}}.group-data-\[variant\=floating\]\:rounded-lg{&:is(:where(.group)[data-variant="floating"] *){border-radius:var(--radius)}}.group-data-\[variant\=floating\]\:border{&:is(:where(.group)[data-variant="floating"] *){border-style:var(--tw-border-style);border-width:1px}}.group-data-\[variant\=floating\]\:border-sidebar-border{&:is(:where(.group)[data-variant="floating"] *){border-color:var(--sidebar-border)}}.group-data-\[vaul-drawer-direction\=bottom\]\/drawer-content\:block{&:is(:where(.group\/drawer-content)[data-vaul-drawer-direction="bottom"] *){display:block}}.group-data-\[viewport\=false\]\/navigation-menu\:top-full{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){top:100%}}.group-data-\[viewport\=false\]\/navigation-menu\:overflow-hidden{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){overflow:hidden}}.group-data-\[viewport\=false\]\/navigation-menu\:rounded-md{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){border-radius:calc(var(--radius) - 2px)}}.group-data-\[viewport\=false\]\/navigation-menu\:border{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){border-style:var(--tw-border-style);border-width:1px}}.group-data-\[viewport\=false\]\/navigation-menu\:bg-popover{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){background-color:var(--popover)}}.group-data-\[viewport\=false\]\/navigation-menu\:text-popover-foreground{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){color:var(--popover-foreground)}}.group-data-\[viewport\=false\]\/navigation-menu\:duration-200{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){--tw-duration:200ms;transition-duration:200ms}}.peer-hover\/menu-button\:text-sidebar-accent-foreground{&:is(:where(.peer\/menu-button):hover ~ *){@media (hover:hover){color:var(--sidebar-accent-foreground)}}}.peer-disabled\:cursor-not-allowed{&:is(:where(.peer):disabled ~ *){cursor:not-allowed}}.peer-disabled\:opacity-50{&:is(:where(.peer):disabled ~ *){opacity:50%}}.peer-disabled\:opacity-70{&:is(:where(.peer):disabled ~ *){opacity:70%}}.peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground{&:is(:where(.peer\/menu-button)[data-active="true"] ~ *){color:var(--sidebar-accent-foreground)}}.selection\:bg-primary{& *::-moz-selection{background-color:var(--primary)}& *::selection{background-color:var(--primary)}&::-moz-selection{background-color:var(--primary)}&::selection{background-color:var(--primary)}}.selection\:text-primary-foreground{& *::-moz-selection{color:var(--primary-foreground)}& *::selection{color:var(--primary-foreground)}&::-moz-selection{color:var(--primary-foreground)}&::selection{color:var(--primary-foreground)}}.file\:inline-flex{&::file-selector-button{display:inline-flex}}.file\:border-0{&::file-selector-button{border-style:var(--tw-border-style);border-width:0px}}.file\:bg-transparent{&::file-selector-button{background-color:transparent}}.file\:text-foreground{&::file-selector-button{color:var(--foreground)}}.placeholder\:text-muted-foreground{&::-moz-placeholder{color:var(--muted-foreground)}&::placeholder{color:var(--muted-foreground)}}.after\:absolute{&::after{content:var(--tw-content);position:absolute}}.after\:left-1\/2{&::after{content:var(--tw-content);left:calc(1/2 * 100%)}}.after\:w-\[2px\]{&::after{content:var(--tw-content);width:2px}}.after\:-translate-x-1\/2{&::after{content:var(--tw-content);--tw-translate-x:calc(calc(1/2 * 100%) * -1);translate:var(--tw-translate-x) var(--tw-translate-y)}}.group-data-\[collapsible\=offcanvas\]\:after\:left-full{&:is(:where(.group)[data-collapsible="offcanvas"] *){&::after{content:var(--tw-content);left:100%}}}.first\:rounded-l-md{&:first-child{border-top-left-radius:calc(var(--radius) - 2px);border-bottom-left-radius:calc(var(--radius) - 2px)}}.first\:border-l{&:first-child{border-left-style:var(--tw-border-style);border-left-width:1px}}.last\:rounded-r-md{&:last-child{border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}}.last\:border-b-0{&:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0px}}.even\:border-l{&:nth-child(even){border-left-style:var(--tw-border-style);border-left-width:1px}}.focus-within\:relative{&:focus-within{position:relative}}.focus-within\:z-20{&:focus-within{z-index:20}}.hover\:bg-accent{&:hover{@media (hover:hover){background-color:var(--accent)}}}.hover\:bg-destructive\/90{&:hover{@media (hover:hover){background-color:var(--destructive);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--destructive) 90%,transparent)}}}}.hover\:bg-muted{&:hover{@media (hover:hover){background-color:var(--muted)}}}.hover\:bg-muted\/25{&:hover{@media (hover:hover){background-color:var(--muted);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--muted) 25%,transparent)}}}}.hover\:bg-muted\/50{&:hover{@media (hover:hover){background-color:var(--muted);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--muted) 50%,transparent)}}}}.hover\:bg-primary{&:hover{@media (hover:hover){background-color:var(--primary)}}}.hover\:bg-primary\/5{&:hover{@media (hover:hover){background-color:var(--primary);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--primary) 5%,transparent)}}}}.hover\:bg-primary\/90{&:hover{@media (hover:hover){background-color:var(--primary);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--primary) 90%,transparent)}}}}.hover\:bg-secondary\/80{&:hover{@media (hover:hover){background-color:var(--secondary);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--secondary) 80%,transparent)}}}}.hover\:bg-sidebar-accent{&:hover{@media (hover:hover){background-color:var(--sidebar-accent)}}}.hover\:bg-transparent{&:hover{@media (hover:hover){background-color:transparent}}}.hover\:text-accent-foreground{&:hover{@media (hover:hover){color:var(--accent-foreground)}}}.hover\:text-foreground{&:hover{@media (hover:hover){color:var(--foreground)}}}.hover\:text-muted-foreground{&:hover{@media (hover:hover){color:var(--muted-foreground)}}}.hover\:text-primary{&:hover{@media (hover:hover){color:var(--primary)}}}.hover\:text-primary-foreground{&:hover{@media (hover:hover){color:var(--primary-foreground)}}}.hover\:text-sidebar-accent-foreground{&:hover{@media (hover:hover){color:var(--sidebar-accent-foreground)}}}.hover\:underline{&:hover{@media (hover:hover){text-decoration-line:underline}}}.hover\:opacity-100{&:hover{@media (hover:hover){opacity:100%}}}.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]{&:hover{@media (hover:hover){--tw-shadow:0 0 0 1px var(--tw-shadow-color,hsl(var(--sidebar-accent)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}}.hover\:ring-4{&:hover{@media (hover:hover){--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}}.hover\:group-data-\[collapsible\=offcanvas\]\:bg-sidebar{&:hover{@media (hover:hover){&:is(:where(.group)[data-collapsible="offcanvas"] *){background-color:var(--sidebar)}}}}.hover\:after\:bg-sidebar-border{&:hover{@media (hover:hover){&::after{content:var(--tw-content);background-color:var(--sidebar-border)}}}}.focus\:z-10{&:focus{z-index:10}}.focus\:bg-accent{&:focus{background-color:var(--accent)}}.focus\:bg-primary{&:focus{background-color:var(--primary)}}.focus\:text-accent-foreground{&:focus{color:var(--accent-foreground)}}.focus\:text-primary-foreground{&:focus{color:var(--primary-foreground)}}.focus\:ring-0{&:focus{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.focus\:ring-1{&:focus{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.focus\:ring-2{&:focus{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.focus\:ring-ring{&:focus{--tw-ring-color:var(--ring)}}.focus\:ring-offset-0{&:focus{--tw-ring-offset-width:0px;--tw-ring-offset-shadow:var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)}}.focus\:ring-offset-2{&:focus{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)}}.focus\:outline-hidden{&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){outline:2px solid transparent;outline-offset:2px}}}.focus\:outline-none{&:focus{--tw-outline-style:none;outline-style:none}}.focus-visible\:z-10{&:focus-visible{z-index:10}}.focus-visible\:border-ring{&:focus-visible{border-color:var(--ring)}}.focus-visible\:ring-1{&:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.focus-visible\:ring-2{&:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.focus-visible\:ring-4{&:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.focus-visible\:ring-\[3px\]{&:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.focus-visible\:ring-destructive\/20{&:focus-visible{--tw-ring-color:var(--destructive);@supports (color:color-mix(in lab,red,red)){--tw-ring-color:color-mix(in oklab,var(--destructive) 20%,transparent)}}}.focus-visible\:ring-ring{&:focus-visible{--tw-ring-color:var(--ring)}}.focus-visible\:ring-ring\/50{&:focus-visible{--tw-ring-color:var(--ring);@supports (color:color-mix(in lab,red,red)){--tw-ring-color:color-mix(in oklab,var(--ring) 50%,transparent)}}}.focus-visible\:ring-offset-1{&:focus-visible{--tw-ring-offset-width:1px;--tw-ring-offset-shadow:var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)}}.focus-visible\:ring-offset-2{&:focus-visible{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)}}.focus-visible\:outline-hidden{&:focus-visible{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){outline:2px solid transparent;outline-offset:2px}}}.focus-visible\:outline-1{&:focus-visible{outline-style:var(--tw-outline-style);outline-width:1px}}.focus-visible\:outline-ring{&:focus-visible{outline-color:var(--ring)}}.focus-visible\:outline-none{&:focus-visible{--tw-outline-style:none;outline-style:none}}.active\:bg-primary\/90{&:active{background-color:var(--primary);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--primary) 90%,transparent)}}}.active\:bg-sidebar-accent{&:active{background-color:var(--sidebar-accent)}}.active\:text-primary-foreground{&:active{color:var(--primary-foreground)}}.active\:text-sidebar-accent-foreground{&:active{color:var(--sidebar-accent-foreground)}}.disabled\:pointer-events-none{&:disabled{pointer-events:none}}.disabled\:cursor-not-allowed{&:disabled{cursor:not-allowed}}.disabled\:cursor-pointer{&:disabled{cursor:pointer}}.disabled\:border-none{&:disabled{--tw-border-style:none;border-style:none}}.disabled\:opacity-50{&:disabled{opacity:50%}}.disabled\:opacity-100{&:disabled{opacity:100%}}.in-data-\[side\=left\]\:cursor-w-resize{:where(*[data-side="left"]) &{cursor:w-resize}}.in-data-\[side\=right\]\:cursor-e-resize{:where(*[data-side="right"]) &{cursor:e-resize}}.has-disabled\:opacity-50{&:has(*:disabled){opacity:50%}}.has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]{&:has(*[data-slot="card-action"]){grid-template-columns:1fr auto}}.has-data-\[variant\=inset\]\:bg-sidebar{&:has(*[data-variant="inset"]){background-color:var(--sidebar)}}.has-\[\>svg\]\:grid-cols-\[calc\(var\(--spacing\)\*4\)_1fr\]{&:has(>svg){grid-template-columns:calc(var(--spacing) * 4) 1fr}}.aria-disabled\:pointer-events-none{&[aria-disabled="true"]{pointer-events:none}}.aria-disabled\:opacity-50{&[aria-disabled="true"]{opacity:50%}}.aria-invalid\:border-destructive{&[aria-invalid="true"]{border-color:var(--destructive)}}.aria-invalid\:ring-destructive\/20{&[aria-invalid="true"]{--tw-ring-color:var(--destructive);@supports (color:color-mix(in lab,red,red)){--tw-ring-color:color-mix(in oklab,var(--destructive) 20%,transparent)}}}.aria-selected\:bg-accent{&[aria-selected="true"]{background-color:var(--accent)}}.aria-selected\:bg-primary{&[aria-selected="true"]{background-color:var(--primary)}}.aria-selected\:text-accent-foreground{&[aria-selected="true"]{color:var(--accent-foreground)}}.aria-selected\:text-muted-foreground{&[aria-selected="true"]{color:var(--muted-foreground)}}.aria-selected\:text-primary-foreground{&[aria-selected="true"]{color:var(--primary-foreground)}}.aria-selected\:opacity-100{&[aria-selected="true"]{opacity:100%}}.data-\[active\=true\]\:z-10{&[data-active="true"]{z-index:10}}.data-\[active\=true\]\:border-ring{&[data-active="true"]{border-color:var(--ring)}}.data-\[active\=true\]\:bg-accent\/50{&[data-active="true"]{background-color:var(--accent);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--accent) 50%,transparent)}}}.data-\[active\=true\]\:bg-primary\/5{&[data-active="true"]{background-color:var(--primary);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--primary) 5%,transparent)}}}.data-\[active\=true\]\:bg-sidebar-accent{&[data-active="true"]{background-color:var(--sidebar-accent)}}.data-\[active\=true\]\:text-accent-foreground{&[data-active="true"]{color:var(--accent-foreground)}}.data-\[active\=true\]\:text-sidebar-accent-foreground{&[data-active="true"]{color:var(--sidebar-accent-foreground)}}.data-\[active\=true\]\:ring-\[3px\]{&[data-active="true"]{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.data-\[active\=true\]\:ring-ring\/50{&[data-active="true"]{--tw-ring-color:var(--ring);@supports (color:color-mix(in lab,red,red)){--tw-ring-color:color-mix(in oklab,var(--ring) 50%,transparent)}}}.data-\[active\=true\]\:hover\:bg-accent{&[data-active="true"]{&:hover{@media (hover:hover){background-color:var(--accent)}}}}.data-\[active\=true\]\:focus\:bg-accent{&[data-active="true"]{&:focus{background-color:var(--accent)}}}.data-\[active\=true\]\:aria-invalid\:border-destructive{&[data-active="true"]{&[aria-invalid="true"]{border-color:var(--destructive)}}}.data-\[active\=true\]\:aria-invalid\:ring-destructive\/20{&[data-active="true"]{&[aria-invalid="true"]{--tw-ring-color:var(--destructive);@supports (color:color-mix(in lab,red,red)){--tw-ring-color:color-mix(in oklab,var(--destructive) 20%,transparent)}}}}.data-\[disabled\]\:pointer-events-none{&[data-disabled]{pointer-events:none}}.data-\[disabled\]\:opacity-50{&[data-disabled]{opacity:50%}}.data-\[disabled\=true\]\:pointer-events-none{&[data-disabled="true"]{pointer-events:none}}.data-\[disabled\=true\]\:opacity-50{&[data-disabled="true"]{opacity:50%}}.data-\[error\=true\]\:text-destructive{&[data-error="true"]{color:var(--destructive)}}.data-\[motion\=from-end\]\:slide-in-from-right-52{&[data-motion="from-end"]{--tw-enter-translate-x:calc(52*var(--spacing))}}.data-\[motion\=from-start\]\:slide-in-from-left-52{&[data-motion="from-start"]{--tw-enter-translate-x:calc(52*var(--spacing)*-1)}}.data-\[motion\=to-end\]\:slide-out-to-right-52{&[data-motion="to-end"]{--tw-exit-translate-x:calc(52*var(--spacing))}}.data-\[motion\=to-start\]\:slide-out-to-left-52{&[data-motion="to-start"]{--tw-exit-translate-x:calc(52*var(--spacing)*-1)}}.data-\[motion\^\=from-\]\:animate-in{&[data-motion^="from-"]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)}}.data-\[motion\^\=from-\]\:fade-in{&[data-motion^="from-"]{--tw-enter-opacity:0}}.data-\[motion\^\=to-\]\:animate-out{&[data-motion^="to-"]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)}}.data-\[motion\^\=to-\]\:fade-out{&[data-motion^="to-"]{--tw-exit-opacity:0}}.data-\[orientation\=horizontal\]\:h-full{&[data-orientation="horizontal"]{height:100%}}.data-\[orientation\=horizontal\]\:h-px{&[data-orientation="horizontal"]{height:1px}}.data-\[orientation\=horizontal\]\:w-full{&[data-orientation="horizontal"]{width:100%}}.data-\[orientation\=vertical\]\:h-full{&[data-orientation="vertical"]{height:100%}}.data-\[orientation\=vertical\]\:w-auto{&[data-orientation="vertical"]{width:auto}}.data-\[orientation\=vertical\]\:w-full{&[data-orientation="vertical"]{width:100%}}.data-\[orientation\=vertical\]\:w-px{&[data-orientation="vertical"]{width:1px}}.data-\[orientation\=vertical\]\:flex-col{&[data-orientation="vertical"]{flex-direction:column}}.data-\[panel-group-direction\=vertical\]\:h-px{&[data-panel-group-direction="vertical"]{height:1px}}.data-\[panel-group-direction\=vertical\]\:w-full{&[data-panel-group-direction="vertical"]{width:100%}}.data-\[panel-group-direction\=vertical\]\:flex-col{&[data-panel-group-direction="vertical"]{flex-direction:column}}.data-\[panel-group-direction\=vertical\]\:after\:w-full{&[data-panel-group-direction="vertical"]{&::after{content:var(--tw-content);width:100%}}}.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2{&[data-panel-group-direction="vertical"]{&::after{content:var(--tw-content);--tw-translate-y:calc(calc(1/2 * 100%) * -1);translate:var(--tw-translate-x) var(--tw-translate-y)}}}.data-\[placeholder\]\:text-muted-foreground{&[data-placeholder]{color:var(--muted-foreground)}}.data-\[selected\=true\]\:bg-accent{&[data-selected="true"]{background-color:var(--accent)}}.data-\[selected\=true\]\:text-accent-foreground{&[data-selected="true"]{color:var(--accent-foreground)}}.data-\[side\=bottom\]\:slide-in-from-top-2{&[data-side="bottom"]{--tw-enter-translate-y:calc(2*var(--spacing)*-1)}}.data-\[side\=left\]\:slide-in-from-right-2{&[data-side="left"]{--tw-enter-translate-x:calc(2*var(--spacing))}}.data-\[side\=right\]\:slide-in-from-left-2{&[data-side="right"]{--tw-enter-translate-x:calc(2*var(--spacing)*-1)}}.data-\[side\=top\]\:slide-in-from-bottom-2{&[data-side="top"]{--tw-enter-translate-y:calc(2*var(--spacing))}}.\*\:data-\[slot\=alert-description\]\:text-destructive\/90{:is(& > *){&[data-slot="alert-description"]{color:var(--destructive);@supports (color:color-mix(in lab,red,red)){color:color-mix(in oklab,var(--destructive) 90%,transparent)}}}}.\*\:data-\[slot\=card\]\:bg-gradient-to-t{:is(& > *){&[data-slot="card"]{--tw-gradient-position:to top in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}}}.\*\:data-\[slot\=card\]\:from-primary\/5{:is(& > *){&[data-slot="card"]{--tw-gradient-from:var(--primary);@supports (color:color-mix(in lab,red,red)){--tw-gradient-from:color-mix(in oklab,var(--primary) 5%,transparent)}--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from) var(--tw-gradient-from-position),var(--tw-gradient-to) var(--tw-gradient-to-position))}}}.\*\:data-\[slot\=card\]\:to-card{:is(& > *){&[data-slot="card"]{--tw-gradient-to:var(--card);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from) var(--tw-gradient-from-position),var(--tw-gradient-to) var(--tw-gradient-to-position))}}}.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:ring-0{:is(& *){&[data-slot="navigation-menu-link"]{&:focus{--tw-ring-shadow:var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}}}.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:outline-none{:is(& *){&[data-slot="navigation-menu-link"]{&:focus{--tw-outline-style:none;outline-style:none}}}}.\*\:data-\[slot\=select-value\]\:line-clamp-1{:is(& > *){&[data-slot="select-value"]{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1}}}.\*\:data-\[slot\=select-value\]\:flex{:is(& > *){&[data-slot="select-value"]{display:flex}}}.\*\:data-\[slot\=select-value\]\:items-center{:is(& > *){&[data-slot="select-value"]{align-items:center}}}.data-\[state\=active\]\:bg-background{&[data-state="active"]{background-color:var(--background)}}.data-\[state\=checked\]\:translate-x-\[calc\(100\%-2px\)\]{&[data-state="checked"]{--tw-translate-x:calc(100% - 2px);translate:var(--tw-translate-x) var(--tw-translate-y)}}.data-\[state\=checked\]\:border-primary{&[data-state="checked"]{border-color:var(--primary)}}.data-\[state\=checked\]\:bg-primary{&[data-state="checked"]{background-color:var(--primary)}}.data-\[state\=checked\]\:text-primary-foreground{&[data-state="checked"]{color:var(--primary-foreground)}}.data-\[state\=closed\]\:animate-accordion-up{&[data-state="closed"]{animation:accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out}}.data-\[state\=closed\]\:animate-out{&[data-state="closed"]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)}}.data-\[state\=closed\]\:duration-300{&[data-state="closed"]{--tw-duration:300ms;transition-duration:300ms}}.data-\[state\=closed\]\:fade-out-0{&[data-state="closed"]{--tw-exit-opacity:calc(0/100);--tw-exit-opacity:0}}.data-\[state\=closed\]\:zoom-out-95{&[data-state="closed"]{--tw-exit-scale:calc(95*1%);--tw-exit-scale:.95}}.data-\[state\=closed\]\:slide-out-to-bottom{&[data-state="closed"]{--tw-exit-translate-y:100%}}.data-\[state\=closed\]\:slide-out-to-left{&[data-state="closed"]{--tw-exit-translate-x:-100%}}.data-\[state\=closed\]\:slide-out-to-right{&[data-state="closed"]{--tw-exit-translate-x:100%}}.data-\[state\=closed\]\:slide-out-to-top{&[data-state="closed"]{--tw-exit-translate-y:-100%}}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:animate-out{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){&[data-state="closed"]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)}}}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:fade-out-0{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){&[data-state="closed"]{--tw-exit-opacity:calc(0/100);--tw-exit-opacity:0}}}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:zoom-out-95{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){&[data-state="closed"]{--tw-exit-scale:calc(95*1%);--tw-exit-scale:.95}}}.data-\[state\=hidden\]\:animate-out{&[data-state="hidden"]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)}}.data-\[state\=hidden\]\:fade-out{&[data-state="hidden"]{--tw-exit-opacity:0}}.data-\[state\=on\]\:bg-accent{&[data-state="on"]{background-color:var(--accent)}}.data-\[state\=on\]\:text-accent-foreground{&[data-state="on"]{color:var(--accent-foreground)}}.data-\[state\=open\]\:animate-accordion-down{&[data-state="open"]{animation:accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out}}.data-\[state\=open\]\:animate-in{&[data-state="open"]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)}}.data-\[state\=open\]\:bg-accent{&[data-state="open"]{background-color:var(--accent)}}.data-\[state\=open\]\:bg-accent\/50{&[data-state="open"]{background-color:var(--accent);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--accent) 50%,transparent)}}}.data-\[state\=open\]\:bg-secondary{&[data-state="open"]{background-color:var(--secondary)}}.data-\[state\=open\]\:bg-sidebar-accent{&[data-state="open"]{background-color:var(--sidebar-accent)}}.data-\[state\=open\]\:text-accent-foreground{&[data-state="open"]{color:var(--accent-foreground)}}.data-\[state\=open\]\:text-muted-foreground{&[data-state="open"]{color:var(--muted-foreground)}}.data-\[state\=open\]\:text-sidebar-accent-foreground{&[data-state="open"]{color:var(--sidebar-accent-foreground)}}.data-\[state\=open\]\:opacity-100{&[data-state="open"]{opacity:100%}}.data-\[state\=open\]\:duration-500{&[data-state="open"]{--tw-duration:500ms;transition-duration:500ms}}.data-\[state\=open\]\:fade-in-0{&[data-state="open"]{--tw-enter-opacity:calc(0/100);--tw-enter-opacity:0}}.data-\[state\=open\]\:zoom-in-90{&[data-state="open"]{--tw-enter-scale:calc(90*1%);--tw-enter-scale:.9}}.data-\[state\=open\]\:zoom-in-95{&[data-state="open"]{--tw-enter-scale:calc(95*1%);--tw-enter-scale:.95}}.data-\[state\=open\]\:slide-in-from-bottom{&[data-state="open"]{--tw-enter-translate-y:100%}}.data-\[state\=open\]\:slide-in-from-left{&[data-state="open"]{--tw-enter-translate-x:-100%}}.data-\[state\=open\]\:slide-in-from-right{&[data-state="open"]{--tw-enter-translate-x:100%}}.data-\[state\=open\]\:slide-in-from-top{&[data-state="open"]{--tw-enter-translate-y:-100%}}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:animate-in{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){&[data-state="open"]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)}}}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:fade-in-0{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){&[data-state="open"]{--tw-enter-opacity:calc(0/100);--tw-enter-opacity:0}}}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:zoom-in-95{&:is(:where(.group\/navigation-menu)[data-viewport="false"] *){&[data-state="open"]{--tw-enter-scale:calc(95*1%);--tw-enter-scale:.95}}}.data-\[state\=open\]\:hover\:bg-accent{&[data-state="open"]{&:hover{@media (hover:hover){background-color:var(--accent)}}}}.data-\[state\=open\]\:hover\:bg-sidebar-accent{&[data-state="open"]{&:hover{@media (hover:hover){background-color:var(--sidebar-accent)}}}}.data-\[state\=open\]\:hover\:text-sidebar-accent-foreground{&[data-state="open"]{&:hover{@media (hover:hover){color:var(--sidebar-accent-foreground)}}}}.data-\[state\=open\]\:focus\:bg-accent{&[data-state="open"]{&:focus{background-color:var(--accent)}}}.data-\[state\=selected\]\:bg-muted{&[data-state="selected"]{background-color:var(--muted)}}.data-\[state\=unchecked\]\:bg-input{&[data-state="unchecked"]{background-color:var(--input)}}.data-\[state\=visible\]\:animate-in{&[data-state="visible"]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)}}.data-\[state\=visible\]\:fade-in{&[data-state="visible"]{--tw-enter-opacity:0}}.data-\[variant\=destructive\]\:text-destructive{&[data-variant="destructive"]{color:var(--destructive)}}.data-\[variant\=destructive\]\:focus\:bg-destructive\/10{&[data-variant="destructive"]{&:focus{background-color:var(--destructive);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--destructive) 10%,transparent)}}}}.data-\[variant\=destructive\]\:focus\:text-destructive{&[data-variant="destructive"]{&:focus{color:var(--destructive)}}}.data-\[variant\=outline\]\:border-l-0{&[data-variant="outline"]{border-left-style:var(--tw-border-style);border-left-width:0px}}.data-\[variant\=outline\]\:first\:border-l{&[data-variant="outline"]{&:first-child{border-left-style:var(--tw-border-style);border-left-width:1px}}}.data-\[vaul-drawer-direction\=bottom\]\:max-h-\[80vh\]{&[data-vaul-drawer-direction="bottom"]{max-height:80vh}}.data-\[vaul-drawer-direction\=bottom\]\:rounded-t-lg{&[data-vaul-drawer-direction="bottom"]{border-top-left-radius:var(--radius);border-top-right-radius:var(--radius)}}.data-\[vaul-drawer-direction\=bottom\]\:border-t{&[data-vaul-drawer-direction="bottom"]{border-top-style:var(--tw-border-style);border-top-width:1px}}.data-\[vaul-drawer-direction\=left\]\:w-3\/4{&[data-vaul-drawer-direction="left"]{width:calc(3/4 * 100%)}}.data-\[vaul-drawer-direction\=left\]\:border-r{&[data-vaul-drawer-direction="left"]{border-right-style:var(--tw-border-style);border-right-width:1px}}.data-\[vaul-drawer-direction\=right\]\:w-3\/4{&[data-vaul-drawer-direction="right"]{width:calc(3/4 * 100%)}}.data-\[vaul-drawer-direction\=right\]\:border-l{&[data-vaul-drawer-direction="right"]{border-left-style:var(--tw-border-style);border-left-width:1px}}.data-\[vaul-drawer-direction\=top\]\:max-h-\[80vh\]{&[data-vaul-drawer-direction="top"]{max-height:80vh}}.data-\[vaul-drawer-direction\=top\]\:rounded-b-lg{&[data-vaul-drawer-direction="top"]{border-bottom-right-radius:var(--radius);border-bottom-left-radius:var(--radius)}}.data-\[vaul-drawer-direction\=top\]\:border-b{&[data-vaul-drawer-direction="top"]{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}}.\@\[540px\]\/card\:block{@container card (width >= 540px){display:block}}.\@\[540px\]\/card\:hidden{@container card (width >= 540px){display:none}}.dark\:border-r{&:is(.dark *){border-right-style:var(--tw-border-style);border-right-width:1px}}.dark\:border-input{&:is(.dark *){border-color:var(--input)}}.dark\:bg-destructive\/60{&:is(.dark *){background-color:var(--destructive);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--destructive) 60%,transparent)}}}.dark\:bg-input\/30{&:is(.dark *){background-color:var(--input);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--input) 30%,transparent)}}}.dark\:text-foreground{&:is(.dark *){color:var(--foreground)}}.dark\:text-muted-foreground{&:is(.dark *){color:var(--muted-foreground)}}.dark\:hover\:bg-accent\/50{&:is(.dark *){&:hover{@media (hover:hover){background-color:var(--accent);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--accent) 50%,transparent)}}}}}.dark\:hover\:bg-input\/50{&:is(.dark *){&:hover{@media (hover:hover){background-color:var(--input);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--input) 50%,transparent)}}}}}.dark\:focus-visible\:ring-destructive\/40{&:is(.dark *){&:focus-visible{--tw-ring-color:var(--destructive);@supports (color:color-mix(in lab,red,red)){--tw-ring-color:color-mix(in oklab,var(--destructive) 40%,transparent)}}}}.dark\:aria-invalid\:ring-destructive\/40{&:is(.dark *){&[aria-invalid="true"]{--tw-ring-color:var(--destructive);@supports (color:color-mix(in lab,red,red)){--tw-ring-color:color-mix(in oklab,var(--destructive) 40%,transparent)}}}}.dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40{&:is(.dark *){&[data-active="true"]{&[aria-invalid="true"]{--tw-ring-color:var(--destructive);@supports (color:color-mix(in lab,red,red)){--tw-ring-color:color-mix(in oklab,var(--destructive) 40%,transparent)}}}}}.dark\:\*\:data-\[slot\=card\]\:bg-card{&:is(.dark *){:is(& > *){&[data-slot="card"]{background-color:var(--card)}}}}.dark\:data-\[state\=active\]\:border-input{&:is(.dark *){&[data-state="active"]{border-color:var(--input)}}}.dark\:data-\[state\=active\]\:bg-input\/30{&:is(.dark *){&[data-state="active"]{background-color:var(--input);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--input) 30%,transparent)}}}}.dark\:data-\[state\=active\]\:text-foreground{&:is(.dark *){&[data-state="active"]{color:var(--foreground)}}}.dark\:data-\[state\=checked\]\:bg-primary{&:is(.dark *){&[data-state="checked"]{background-color:var(--primary)}}}.dark\:data-\[state\=checked\]\:bg-primary-foreground{&:is(.dark *){&[data-state="checked"]{background-color:var(--primary-foreground)}}}.dark\:data-\[state\=unchecked\]\:bg-foreground{&:is(.dark *){&[data-state="unchecked"]{background-color:var(--foreground)}}}.dark\:data-\[state\=unchecked\]\:bg-input\/80{&:is(.dark *){&[data-state="unchecked"]{background-color:var(--input);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--input) 80%,transparent)}}}}.dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20{&:is(.dark *){&[data-variant="destructive"]{&:focus{background-color:var(--destructive);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--destructive) 20%,transparent)}}}}}.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground{& .recharts-cartesian-axis-tick text{fill:var(--muted-foreground)}}.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50{& .recharts-cartesian-grid line[stroke='#ccc']{stroke:var(--border);@supports (color:color-mix(in lab,red,red)){stroke:color-mix(in oklab,var(--border) 50%,transparent)}}}.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border{& .recharts-curve.recharts-tooltip-cursor{stroke:var(--border)}}.\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent{& .recharts-dot[stroke='#fff']{stroke:transparent}}.\[\&_\.recharts-layer\]\:outline-hidden{& .recharts-layer{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){outline:2px solid transparent;outline-offset:2px}}}.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border{& .recharts-polar-grid [stroke='#ccc']{stroke:var(--border)}}.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted{& .recharts-radial-bar-background-sector{fill:var(--muted)}}.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted{& .recharts-rectangle.recharts-tooltip-cursor{fill:var(--muted)}}.\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border{& .recharts-reference-line [stroke='#ccc']{stroke:var(--border)}}.\[\&_\.recharts-sector\]\:outline-hidden{& .recharts-sector{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){outline:2px solid transparent;outline-offset:2px}}}.\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent{& .recharts-sector[stroke='#fff']{stroke:transparent}}.\[\&_\.recharts-surface\]\:outline-hidden{& .recharts-surface{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){outline:2px solid transparent;outline-offset:2px}}}.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground{& [cmdk-group-heading]{color:var(--muted-foreground)}}.\[\&_svg\]\:pointer-events-none{& svg{pointer-events:none}}.\[\&_svg\]\:invisible{& svg{visibility:hidden}}.\[\&_svg\]\:shrink-0{& svg{flex-shrink:0}}.\[\&_svg\]\:text-muted-foreground{& svg{color:var(--muted-foreground)}}.\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground{& svg:not([class*='text-']){color:var(--muted-foreground)}}.\[\&_tr\]\:border-b{& tr{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}}.\[\&_tr\:last-child\]\:border-0{& tr:last-child{border-style:var(--tw-border-style);border-width:0px}}.\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md{&:has(>.day-range-end){border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}}.\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md{&:has(>.day-range-start){border-top-left-radius:calc(var(--radius) - 2px);border-bottom-left-radius:calc(var(--radius) - 2px)}}.\[\&\:has\(\[aria-selected\]\)\]\:rounded-md{&:has([aria-selected]){border-radius:calc(var(--radius) - 2px)}}.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent{&:has([aria-selected]){background-color:var(--accent)}}.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md{&:first-child{&:has([aria-selected]){border-top-left-radius:calc(var(--radius) - 2px);border-bottom-left-radius:calc(var(--radius) - 2px)}}}.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md{&:last-child{&:has([aria-selected]){border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}}}.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md{&:has([aria-selected].day-range-end){border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}}.\*\:\[span\]\:last\:flex{:is(& > *){&:is(span){&:last-child{display:flex}}}}.\*\:\[span\]\:last\:items-center{:is(& > *){&:is(span){&:last-child{align-items:center}}}}.data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive{&[data-variant="destructive"]{:is(& > *){&:is(svg){color:var(--destructive) !important}}}}.\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\]{&>[role=checkbox]{--tw-translate-y:2px;translate:var(--tw-translate-x) var(--tw-translate-y)}}.\[\&\>button\]\:hidden{&>button{display:none}}.\[\&\>span\:first-child\]\:left-auto{&>span:first-child{left:auto}}.\[\&\>span\:last-child\]\:truncate{&>span:last-child{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}}.\[\&\>svg\]\:pointer-events-none{&>svg{pointer-events:none}}.\[\&\>svg\]\:shrink-0{&>svg{flex-shrink:0}}.\[\&\>svg\]\:text-current{&>svg{color:currentcolor}}.\[\&\>svg\]\:text-muted-foreground{&>svg{color:var(--muted-foreground)}}.\[\&\>svg\]\:text-sidebar-accent-foreground{&>svg{color:var(--sidebar-accent-foreground)}}.\[\&\>tr\]\:last\:border-b-0{&>tr{&:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0px}}}.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90{&[data-panel-group-direction=vertical]>div{rotate:90deg}}.\[\&\[data-state\=closed\]\>button\]\:hidden{&[data-state=closed]>button{display:none}}.\[\&\[data-state\=open\]\>\.alert\]\:hidden{&[data-state=open]>.alert{display:none}}.\[\&\[data-state\=open\]\>svg\]\:rotate-180{&[data-state=open]>svg{rotate:180deg}}.\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize{[data-side=left][data-state=collapsed] &{cursor:e-resize}}.\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize{[data-side=right][data-state=collapsed] &{cursor:w-resize}}.\[a\&\]\:hover\:bg-accent{a&{&:hover{@media (hover:hover){background-color:var(--accent)}}}}.\[a\&\]\:hover\:bg-destructive\/90{a&{&:hover{@media (hover:hover){background-color:var(--destructive);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--destructive) 90%,transparent)}}}}}.\[a\&\]\:hover\:bg-primary\/90{a&{&:hover{@media (hover:hover){background-color:var(--primary);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--primary) 90%,transparent)}}}}}.\[a\&\]\:hover\:bg-secondary\/90{a&{&:hover{@media (hover:hover){background-color:var(--secondary);@supports (color:color-mix(in lab,red,red)){background-color:color-mix(in oklab,var(--secondary) 90%,transparent)}}}}}.\[a\&\]\:hover\:text-accent-foreground{a&{&:hover{@media (hover:hover){color:var(--accent-foreground)}}}}:root{--radius:0.625rem;--background:oklch(1 0 0);--foreground:oklch(0.145 0 0);--card:oklch(1 0 0);--card-foreground:oklch(0.145 0 0);--popover:oklch(1 0 0);--popover-foreground:oklch(0.145 0 0);--primary:oklch(0.205 0 0);--primary-foreground:oklch(0.985 0 0);--secondary:oklch(0.97 0 0);--secondary-foreground:oklch(0.205 0 0);--muted:oklch(0.97 0 0);--muted-foreground:oklch(0.556 0 0);--accent:oklch(0.97 0 0);--accent-foreground:oklch(0.205 0 0);--destructive:oklch(0.577 0.245 27.325);--border:oklch(0.922 0 0);--input:oklch(0.922 0 0);--ring:oklch(0.708 0 0);--chart-1:oklch(0.646 0.222 41.116);--chart-2:oklch(0.6 0.118 184.704);--chart-3:oklch(0.398 0.07 227.392);--chart-4:oklch(0.828 0.189 84.429);--chart-5:oklch(0.769 0.188 70.08);--sidebar:oklch(0.985 0 0);--sidebar-foreground:oklch(0.145 0 0);--sidebar-primary:oklch(0.205 0 0);--sidebar-primary-foreground:oklch(0.985 0 0);--sidebar-accent:oklch(0.97 0 0);--sidebar-accent-foreground:oklch(0.205 0 0);--sidebar-border:oklch(0.922 0 0);--sidebar-ring:oklch(0.708 0 0)}.dark{--background:oklch(0.145 0 0);--foreground:oklch(0.985 0 0);--card:oklch(0.205 0 0);--card-foreground:oklch(0.985 0 0);--popover:oklch(0.269 0 0);--popover-foreground:oklch(0.985 0 0);--primary:oklch(0.922 0 0);--primary-foreground:oklch(0.205 0 0);--secondary:oklch(0.269 0 0);--secondary-foreground:oklch(0.985 0 0);--muted:oklch(0.269 0 0);--muted-foreground:oklch(0.708 0 0);--accent:oklch(0.371 0 0);--accent-foreground:oklch(0.985 0 0);--destructive:oklch(0.704 0.191 22.216);--border:oklch(1 0 0 / 10%);--input:oklch(1 0 0 / 15%);--ring:oklch(0.556 0 0);--chart-1:oklch(0.488 0.243 264.376);--chart-2:oklch(0.696 0.17 162.48);--chart-3:oklch(0.769 0.188 70.08);--chart-4:oklch(0.627 0.265 303.9);--chart-5:oklch(0.645 0.246 16.439);--sidebar:oklch(0.205 0 0);--sidebar-foreground:oklch(0.985 0 0);--sidebar-primary:oklch(0.488 0.243 264.376);--sidebar-primary-foreground:oklch(0.985 0 0);--sidebar-accent:oklch(0.269 0 0);--sidebar-accent-foreground:oklch(0.985 0 0);--sidebar-border:oklch(1 0 0 / 10%);--sidebar-ring:oklch(0.439 0 0)}@layer base{*{border-color:var(--border);outline-color:var(--ring);@supports (color:color-mix(in lab,red,red)){outline-color:color-mix(in oklab,var(--ring) 50%,transparent)}}body{background-color:var(--background);color:var(--foreground)}}::view-transition-old(root),::view-transition-new(root){animation:none;mix-blend-mode:normal}::view-transition-old(root){z-index:0}::view-transition-new(root){z-index:1}@keyframes reveal{from{clip-path:circle(0% at var(--x,50%) var(--y,50%));opacity:0.7}to{clip-path:circle(150% at var(--x,50%) var(--y,50%));opacity:1}}::view-transition-new(root){animation:reveal 0.4s ease-in-out forwards}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0px}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-leading{syntax:"*";inherits:false}@property --tw-ordinal{syntax:"*";inherits:false}@property --tw-slashed-zero{syntax:"*";inherits:false}@property --tw-numeric-figure{syntax:"*";inherits:false}@property --tw-numeric-spacing{syntax:"*";inherits:false}@property --tw-numeric-fraction{syntax:"*";inherits:false}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@property --tw-content{syntax:"*";initial-value:"";inherits:false}@keyframes enter{from{opacity:var(--tw-enter-opacity,1);transform:translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity,1);transform:translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0))}}@keyframes accordion-down{from{height:0}to{height:var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,auto)))}}@keyframes accordion-up{from{height:var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,auto)))}to{height:0}}@keyframes caret-blink{0%,70%,100%{opacity:1}20%,50%{opacity:0}}@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,::before,::after,::backdrop{--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-leading:initial;--tw-ordinal:initial;--tw-slashed-zero:initial;--tw-numeric-figure:initial;--tw-numeric-spacing:initial;--tw-numeric-fraction:initial;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-duration:initial;--tw-ease:initial;--tw-content:""}}}