/**
 * Tailwind CSS Compiler for Next.js Frontend
 *
 * This script compiles Tailwind CSS for the Next.js frontend.
 * It's designed to work with Tailwind CSS v4 and the @tailwindcss/postcss package.
 */
const path = require('path');
const fs = require('fs');

// Get absolute paths
const frontendDir = path.resolve(__dirname, '..');
const srcDir = path.join(frontendDir, 'src');
const appDir = path.join(srcDir, 'app');
const outputDir = path.join(frontendDir, 'public/css');
const inputCssFile = path.join(appDir, 'globals.css');

// Check if we're in watch mode
const isWatchMode = process.argv.includes('--watch');

// Ensure the output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

console.log('Compiling TailwindCSS...');
console.log(`Frontend directory: ${frontendDir}`);
console.log(`Input CSS file: ${inputCssFile}`);
console.log(`Output directory: ${outputDir}`);
console.log(`Watch mode: ${isWatchMode ? 'enabled' : 'disabled'}`);

// Function to compile Tailwind CSS using the API directly
function compileTailwind() {
  try {
    // Read the input CSS file
    const inputCss = fs.readFileSync(inputCssFile, 'utf8');
    console.log(`Read input CSS file (${inputCss.length} bytes)`);

    // Load the Tailwind CSS and PostCSS modules directly
    const tailwindcss = require('@tailwindcss/postcss');
    const autoprefixer = require('autoprefixer');
    const postcss = require('postcss');

    console.log('Loaded PostCSS and Tailwind CSS modules');

    // Load the Tailwind config
    const tailwindConfig = require(path.join(frontendDir, 'tailwind.config.js'));
    console.log('Loaded Tailwind CSS configuration');

    // Create the PostCSS processor
    const processor = postcss([
      tailwindcss(tailwindConfig),
      autoprefixer
    ]);

    console.log('Created PostCSS processor');

    // Process the CSS
    processor.process(inputCss, {
      from: inputCssFile,
      to: path.join(outputDir, 'tailwind.css')
    }).then(result => {
      // Write the output CSS file
      fs.writeFileSync(path.join(outputDir, 'tailwind.css'), result.css);
      console.log(`Written output CSS file (${result.css.length} bytes)`);

      if (result.map) {
        fs.writeFileSync(path.join(outputDir, 'tailwind.css.map'), result.map.toString());
        console.log('Written source map file');
      }

      console.log('TailwindCSS compilation completed successfully.');

      if (!isWatchMode) {
        process.exit(0);
      }
    }).catch(error => {
      console.error('Error processing CSS:', error);
      if (!isWatchMode) {
        process.exit(1);
      }
    });

    // If in watch mode, set up a file watcher
    if (isWatchMode) {
      console.log('Setting up file watcher...');
      const chokidar = require('chokidar');

      // Watch the input CSS file and all files that might affect the output
      const watcher = chokidar.watch([
        inputCssFile,
        path.join(frontendDir, 'tailwind.config.js'),
        path.join(frontendDir, 'postcss.config.js'),
        path.join(srcDir, '**/*.{js,jsx,ts,tsx}')
      ]);

      watcher.on('change', (changedPath) => {
        console.log(`File changed: ${changedPath}`);
        compileTailwind();
      });

      console.log('Watching for file changes...');
    }

    return true;
  } catch (error) {
    console.error('Error compiling TailwindCSS:', error);
    if (!isWatchMode) {
      process.exit(1);
    }
    return false;
  }
}

// Run the compilation
compileTailwind();
