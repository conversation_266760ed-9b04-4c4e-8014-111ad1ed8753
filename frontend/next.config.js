/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configure image domains
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'utfs.io',
        port: ''
      },
      {
        protocol: 'https',
        hostname: 'api.slingacademy.com',
        port: ''
      }
    ]
  },

  // Transpile packages
  transpilePackages: ['geist'],

  // Disable React strict mode for compatibility
  reactStrictMode: false,

  // Configure server settings
  experimental: {
    // Disable server actions for compatibility with NestJS integration
    serverActions: {
      allowedOrigins: ['localhost:3000'],
      enabled: false
    }
  },

  // External packages that should be transpiled
  serverExternalPackages: [],

  // Configure API routes
  async rewrites() {
    return [
      // Forward API requests to NestJS
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },
    ];
  },
};

module.exports = nextConfig;
