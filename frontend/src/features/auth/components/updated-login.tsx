'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { SmsApi } from '@/api/auth-api';
import { useAuth } from '@/hooks/use-auth';
import { toast } from 'sonner';

// UI Components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Define response types
interface CaptchaResponseData {
  token: string;
  svg: string;
}

interface CaptchaResponse {
  success: boolean;
  data: CaptchaResponseData;
}

interface SmsResponse {
  success: boolean;
  message: string;
}

export default function LoginPage() {
  const router = useRouter();
  const smsApi = new SmsApi();
  const { loginWithPassword, loginWithSms } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'password' | 'phone'>('password');
  const [countdown, setCountdown] = useState(0);

  // Password login form
  const [passwordForm, setPasswordForm] = useState({
    account: '',
    password: '',
    rememberMe: false
  });

  // Phone login form
  const [phoneForm, setPhoneForm] = useState({
    phone: '',
    smsCode: '',
    rememberMe: false
  });

  // Captcha
  const [captcha, setCaptcha] = useState({
    token: '',
    svg: '',
    text: ''
  });

  useEffect(() => {
    // Get captcha on load
    fetchCaptcha();
  }, []);

  // Fetch captcha
  const fetchCaptcha = async () => {
    try {
      const response = await smsApi.getCaptcha() as CaptchaResponse;
      if (response && response.data) {
        setCaptcha(prev => ({
          ...prev,
          token: response.data.token,
          svg: response.data.svg
        }));
      }
    } catch (error) {
      console.error('Failed to fetch captcha:', error);
      toast.error('Failed to get captcha', {
        description: 'Please refresh the page and try again'
      });
    }
  };

  // Password form handler
  const handlePasswordFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Phone form handler
  const handlePhoneFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPhoneForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Captcha input handler
  const handleCaptchaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCaptcha(prev => ({
      ...prev,
      text: e.target.value
    }));
  };

  // Password checkbox handler
  const handlePasswordCheckboxChange = (checked: boolean) => {
    setPasswordForm(prev => ({
      ...prev,
      rememberMe: checked
    }));
  };

  // Phone checkbox handler
  const handlePhoneCheckboxChange = (checked: boolean) => {
    setPhoneForm(prev => ({
      ...prev,
      rememberMe: checked
    }));
  };

  // Send SMS code
  const sendSmsCode = async () => {
    if (!phoneForm.phone) {
      toast.error('Please enter your phone number');
      return;
    }

    if (!captcha.text) {
      toast.error('Please enter the captcha code');
      return;
    }

    try {
      const response = await smsApi.sendSmsCode({
        phone: phoneForm.phone,
        captchaToken: captcha.token,
        captchaText: captcha.text
      }) as SmsResponse;

      toast.success('Verification code sent', {
        description: 'Please check your phone for the SMS code'
      });

      // Start countdown
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

    } catch (error) {
      console.error('Failed to send SMS code:', error);
      toast.error('Failed to send verification code', {
        description: 'Please try again later'
      });
      // Refresh captcha
      fetchCaptcha();
    }
  };

  // Password login submit
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await loginWithPassword(
        passwordForm.account,
        passwordForm.password,
        passwordForm.rememberMe
      );

      toast.success('Login successful', {
        description: 'Welcome back!'
      });

      // Redirect to dashboard
      router.push('/dashboard');
    } catch (error) {
      console.error('Login failed:', error);
      toast.error('Login failed', {
        description: 'Invalid username or password, please try again'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Phone login submit
  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await loginWithSms(
        phoneForm.phone,
        phoneForm.smsCode,
        phoneForm.rememberMe
      );

      toast.success('Login successful', {
        description: 'Welcome back!'
      });

      // Redirect to dashboard
      router.push('/dashboard');
    } catch (error) {
      console.error('Login failed:', error);
      toast.error('Login failed', {
        description: 'Invalid verification code or it has expired, please try again'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl text-center">Sign In</CardTitle>
        <CardDescription className="text-center">
          Choose your login method
        </CardDescription>
      </CardHeader>

      <Tabs defaultValue="password" value={activeTab} onValueChange={(value) => setActiveTab(value as 'password' | 'phone')} className="w-full">
        <TabsList className="grid w-[90%] grid-cols-2 mb-4 mx-auto">
          <TabsTrigger value="password">Password</TabsTrigger>
          <TabsTrigger value="phone">SMS</TabsTrigger>
        </TabsList>

        <TabsContent value="password">
          <form onSubmit={handlePasswordSubmit}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="account">Account</Label>
                <Input
                  id="account"
                  name="account"
                  placeholder="Username/Phone/Email"
                  value={passwordForm.account}
                  onChange={handlePasswordFormChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    href="/forgot-password"
                    className="text-sm text-muted-foreground hover:text-foreground"
                  >
                    Forgot password?
                  </Link>
                </div>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Enter your password"
                  value={passwordForm.password}
                  onChange={handlePasswordFormChange}
                  required
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="password-remember"
                  checked={passwordForm.rememberMe}
                  onCheckedChange={handlePasswordCheckboxChange}
                />
                <label
                  htmlFor="password-remember"
                  className="text-sm font-medium leading-none"
                >
                  Remember me
                </label>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? 'Signing in...' : 'Sign in'}
              </Button>
            </CardFooter>
          </form>
        </TabsContent>

        <TabsContent value="phone">
          <form onSubmit={handlePhoneSubmit}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  name="phone"
                  placeholder="Enter your phone number"
                  value={phoneForm.phone}
                  onChange={handlePhoneFormChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="captcha">Captcha</Label>
                <div className="flex space-x-2 items-center">
                  <Input
                    id="captcha"
                    name="captcha"
                    placeholder="Enter captcha"
                    value={captcha.text}
                    onChange={handleCaptchaChange}
                    required
                    className="flex-1"
                  />
                  <div
                    className="w-[150px] h-10 flex-shrink-0 cursor-pointer border rounded-md overflow-hidden flex items-center justify-center bg-white"
                    onClick={fetchCaptcha}
                    title="Click to refresh captcha"
                    dangerouslySetInnerHTML={{ __html: captcha.svg }}
                    style={{ display: 'flex', alignItems: 'center' }}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="smsCode">SMS Verification Code</Label>
                <div className="flex space-x-2">
                  <Input
                    id="smsCode"
                    name="smsCode"
                    placeholder="Enter SMS code"
                    value={phoneForm.smsCode}
                    onChange={handlePhoneFormChange}
                    required
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    className="min-w-[120px] flex-shrink-0"
                    onClick={sendSmsCode}
                    disabled={countdown > 0}
                  >
                    {countdown > 0 ? `${countdown}s` : 'Get Code'}
                  </Button>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="phone-remember"
                  checked={phoneForm.rememberMe}
                  onCheckedChange={handlePhoneCheckboxChange}
                />
                <label
                  htmlFor="phone-remember"
                  className="text-sm font-medium leading-none"
                >
                  Remember me
                </label>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? 'Signing in...' : 'Sign in'}
              </Button>
            </CardFooter>
          </form>
        </TabsContent>
      </Tabs>

      <div className="p-6 pt-0 text-center text-sm">
        Don't have an account?{' '}
        <Link
          href="/auth/sign-up"
          className="text-primary hover:underline font-medium"
        >
          Sign up
        </Link>
      </div>
    </Card>
  );
}
