'use client';

import { buttonVariants } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { GitHubLogoIcon } from '@radix-ui/react-icons';
import { IconStar } from '@tabler/icons-react';
import Link from 'next/link';
import LoginPage from './updated-login';

export default function CustomSignInViewPage({ stars }: { stars: number }) {
  return (
    <div className='relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0'>
      <Link
        href='/examples/authentication'
        className={cn(
          buttonVariants({ variant: 'ghost' }),
          'absolute top-4 right-4 hidden md:top-8 md:right-8'
        )}
      >
        Login
      </Link>
      <div className='bg-muted relative hidden h-full flex-col p-10 text-white lg:flex dark:border-r'>
        <div className='absolute inset-0 bg-zinc-900' />
        <div className='relative z-20 flex items-center text-lg font-medium'>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            viewBox='0 0 24 24'
            fill='none'
            stroke='currentColor'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
            className='mr-2 h-6 w-6'
          >
            <path d='M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3' />
          </svg>
          Mini Shop
        </div>
        <div className='relative z-20 mt-auto'>
          <blockquote className='space-y-2'>
            <p className='text-lg'>
              &ldquo;This dashboard provides powerful tools to manage your shop efficiently and effectively.&rdquo;
            </p>
            <footer className='text-sm'>Mini Shop Admin</footer>
          </blockquote>
        </div>
      </div>
      <div className='lg:p-8'>
        <div className='mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]'>
          <div className='flex flex-col space-y-2 text-center'>
            <h1 className='text-2xl font-semibold tracking-tight'>
              Welcome back
            </h1>
            <p className='text-sm text-muted-foreground'>
              Sign in to your account to continue
            </p>
          </div>
          <div className='grid gap-6'>
            <div className='grid gap-2'>
              <div className='grid gap-1'>
                <Link
                  href='https://github.com/kiranism/next-shadcn-dashboard-starter'
                  target='_blank'
                  className='inline-flex items-center justify-center gap-1 text-sm text-muted-foreground'
                >
                  <GitHubLogoIcon className='h-3.5 w-3.5' />
                  <span className='font-medium'>kiranism/next-shadcn-dashboard-starter</span>
                  <IconStar className='h-3.5 w-3.5 fill-primary text-primary' />
                  <span className='font-display font-medium'>{stars}</span>
                </Link>
              </div>
            </div>

            {/* Replace the Clerk sign-in form with your custom login component */}
            <LoginPage />

            <p className='text-muted-foreground px-8 text-center text-sm'>
              By clicking continue, you agree to our{' '}
              <Link
                href='/terms'
                className='hover:text-primary underline underline-offset-4'
              >
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link
                href='/privacy'
                className='hover:text-primary underline underline-offset-4'
              >
                Privacy Policy
              </Link>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
