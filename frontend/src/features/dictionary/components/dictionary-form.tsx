'use client';

import { DictionaryApi } from '@/api/dictionary-api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

interface DictionaryEntry {
  id: number;
  key: string;
  description: string;
  enabled: boolean;
  values: DictionaryValue[];
}

interface DictionaryValue {
  id: number;
  value: string;
  label: string;
  orderNum: number;
}

const formSchema = z.object({
  key: z.string().min(1, 'Key is required'),
  description: z.string().optional(),
  enabled: z.boolean().default(true),
});

export default function DictionaryForm({
  initialData,
  pageTitle
}: {
  initialData: DictionaryEntry | null;
  pageTitle: string;
}) {
  const [loading, setLoading] = useState(false);

  const router = useRouter();
  const dictionaryApi = new DictionaryApi();

  const defaultValues = {
    key: initialData?.key || '',
    description: initialData?.description || '',
    enabled: initialData?.enabled ?? true,
  };

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    values: defaultValues
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setLoading(true);
    try {
      if (initialData) {
        // Update existing dictionary entry
        await dictionaryApi.updateDictionary(initialData.id, values);
        toast.success('Dictionary entry updated successfully');
        router.push(`/dashboard/dictionary/${initialData.id}`);
      } else {
        // Create new dictionary entry
        const response = await dictionaryApi.createDictionary(values);
        toast.success('Dictionary entry created successfully');
        router.push(`/dashboard/dictionary/${response.id}`);
      }
    } catch (error) {
      console.error('Error saving dictionary entry:', error);
      toast.error('Failed to save dictionary entry');
    } finally {
      setLoading(false);
    }
  }

  return (
    <Card className='mx-auto w-full'>
      <CardHeader>
        <CardTitle className='text-left text-2xl font-bold'>
          {pageTitle}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
            <FormField
              control={form.control}
              name='key'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Key</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter dictionary key'
                      {...field}
                      disabled={loading || !!initialData}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Enter dictionary description'
                      className='resize-none'
                      {...field}
                      disabled={loading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='enabled'
              render={({ field }) => (
                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                  <div className='space-y-0.5'>
                    <FormLabel className='text-base'>Enabled</FormLabel>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={loading}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <Button type='submit' disabled={loading}>
              {initialData ? 'Update' : 'Create'} Dictionary Entry
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
