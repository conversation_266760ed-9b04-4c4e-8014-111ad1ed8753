'use client';

import { DictionaryApi } from '@/api/dictionary-api';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';

interface DictionaryEntry {
  id: number;
  key: string;
  description: string;
  enabled: boolean;
  values: DictionaryValue[];
}

interface DictionaryValue {
  id: number;
  value: string;
  label: string;
  orderNum: number;
}

const formSchema = z.object({
  key: z.string().min(1, 'Key is required'),
  description: z.string().optional(),
  enabled: z.boolean().default(true),
});

const valueFormSchema = z.object({
  value: z.string().min(1, 'Value is required'),
  label: z.string().optional(),
});

export default function DictionaryDetailPage({ id }: { id: string }) {
  const [entry, setEntry] = useState<DictionaryEntry | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedValueId, setSelectedValueId] = useState<number | null>(null);
  const [addValueDialogOpen, setAddValueDialogOpen] = useState(false);
  const [newValue, setNewValue] = useState({ value: '', label: '' });

  const dictionaryApi = new DictionaryApi();

  const fetchEntry = async () => {
    setLoading(true);
    try {
      const response = await dictionaryApi.getDictionaryById(parseInt(id));
      setEntry(response);
      form.reset({
        key: response.key,
        description: response.description || '',
        enabled: response.enabled,
      });
    } catch (error) {
      console.error('Error fetching dictionary entry:', error);
      toast.error('Failed to load dictionary entry');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEntry();
  }, [id]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      key: '',
      description: '',
      enabled: true,
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setSaving(true);
    try {
      await dictionaryApi.updateDictionary(parseInt(id), values);
      toast.success('Dictionary entry updated successfully');
      fetchEntry();
    } catch (error) {
      console.error('Error updating dictionary entry:', error);
      toast.error('Failed to update dictionary entry');
    } finally {
      setSaving(false);
    }
  }

  const handleDeleteValue = async (valueId: number) => {
    try {
      await dictionaryApi.deleteDictionaryValue(valueId);
      toast.success('Dictionary value deleted successfully');
      fetchEntry();
    } catch (error) {
      console.error('Error deleting dictionary value:', error);
      toast.error('Failed to delete dictionary value');
    } finally {
      setDeleteDialogOpen(false);
      setSelectedValueId(null);
    }
  };

  const confirmDeleteValue = (valueId: number) => {
    setSelectedValueId(valueId);
    setDeleteDialogOpen(true);
  };

  const handleAddValue = async () => {
    if (!newValue.value) {
      toast.error('Value is required');
      return;
    }

    try {
      await dictionaryApi.addDictionaryValue(parseInt(id), newValue);
      toast.success('Dictionary value added successfully');
      setNewValue({ value: '', label: '' });
      setAddValueDialogOpen(false);
      fetchEntry();
    } catch (error) {
      console.error('Error adding dictionary value:', error);
      toast.error('Failed to add dictionary value');
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!entry) {
    return <div>Dictionary entry not found</div>;
  }

  return (
    <Tabs defaultValue='details'>
      <TabsList className='mb-4'>
        <TabsTrigger value='details'>Details</TabsTrigger>
        <TabsTrigger value='values'>Values</TabsTrigger>
      </TabsList>

      <TabsContent value='details'>
        <Card className='mx-auto w-full'>
          <CardHeader>
            <CardTitle className='text-left text-2xl font-bold'>
              Dictionary Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
                <FormField
                  control={form.control}
                  name='key'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Key</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Enter dictionary key'
                          {...field}
                          disabled={true} // Key is read-only in detail view
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='Enter dictionary description'
                          className='resize-none'
                          {...field}
                          disabled={saving}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='enabled'
                  render={({ field }) => (
                    <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                      <div className='space-y-0.5'>
                        <FormLabel className='text-base'>Enabled</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={saving}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <Button type='submit' disabled={saving}>
                  Save Changes
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value='values'>
        <Card className='mx-auto w-full'>
          <CardHeader className='flex flex-row items-center justify-between'>
            <CardTitle className='text-left text-2xl font-bold'>
              Dictionary Values
            </CardTitle>
            <Dialog open={addValueDialogOpen} onOpenChange={setAddValueDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <IconPlus className='mr-2 h-4 w-4' /> Add Value
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Value</DialogTitle>
                  <DialogDescription>
                    Add a new value to this dictionary entry.
                  </DialogDescription>
                </DialogHeader>
                <div className='grid gap-4 py-4'>
                  <div className='grid grid-cols-4 items-center gap-4'>
                    <Label htmlFor='value' className='text-right'>
                      Value
                    </Label>
                    <Input
                      id='value'
                      value={newValue.value}
                      onChange={(e) => setNewValue({ ...newValue, value: e.target.value })}
                      className='col-span-3'
                    />
                  </div>
                  <div className='grid grid-cols-4 items-center gap-4'>
                    <Label htmlFor='label' className='text-right'>
                      Label
                    </Label>
                    <Input
                      id='label'
                      value={newValue.label}
                      onChange={(e) => setNewValue({ ...newValue, label: e.target.value })}
                      className='col-span-3'
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button onClick={handleAddValue}>Add Value</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </CardHeader>
          <CardContent>
            {entry.values && entry.values.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Value</TableHead>
                    <TableHead>Label</TableHead>
                    <TableHead>Order</TableHead>
                    <TableHead className='text-right'>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {entry.values.map((value) => (
                    <TableRow key={value.id}>
                      <TableCell>{value.value}</TableCell>
                      <TableCell>{value.label || '-'}</TableCell>
                      <TableCell>{value.orderNum}</TableCell>
                      <TableCell className='text-right'>
                        <Button
                          variant='ghost'
                          size='icon'
                          onClick={() => confirmDeleteValue(value.id)}
                        >
                          <IconTrash className='h-4 w-4' />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className='text-center py-4 text-muted-foreground'>
                No values found. Add a value to get started.
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this
              dictionary value.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => selectedValueId && handleDeleteValue(selectedValueId)}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Tabs>
  );
}
