'use client';

import { DictionaryApi } from '@/api/dictionary-api';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/table/data-table';
import { DataTableColumnHeader } from '@/components/ui/table/data-table-column-header';
import { DataTableToolbar } from '@/components/ui/table/data-table-toolbar';
import { useDataTable } from '@/hooks/use-data-table';
import { Badge } from '@/components/ui/badge';
import { ColumnDef } from '@tanstack/react-table';
import { parseAsInteger, useQueryState } from 'nuqs';
import { useEffect, useState } from 'react';
import { IconEdit, IconEye, IconTrash } from '@tabler/icons-react';
import Link from 'next/link';
import { toast } from 'sonner';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface DictionaryEntry {
  id: number;
  key: string;
  description: string;
  enabled: boolean;
  values: DictionaryValue[];
  createdAt: string;
  updatedAt: string;
}

interface DictionaryValue {
  id: number;
  value: string;
  label: string;
  orderNum: number;
}

export default function DictionaryListingPage() {
  const [data, setData] = useState<DictionaryEntry[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedId, setSelectedId] = useState<number | null>(null);

  const dictionaryApi = new DictionaryApi();

  const [pageSize] = useQueryState('perPage', parseAsInteger.withDefault(10));

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await dictionaryApi.getAllDictionaries();
      setData(response.data);
      setTotalItems(response.total);
    } catch (error) {
      console.error('Error fetching dictionary entries:', error);
      toast.error('Failed to load dictionary entries');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleDelete = async (id: number) => {
    try {
      await dictionaryApi.deleteDictionary(id);
      toast.success('Dictionary entry deleted successfully');
      fetchData();
    } catch (error) {
      console.error('Error deleting dictionary entry:', error);
      toast.error('Failed to delete dictionary entry');
    } finally {
      setDeleteDialogOpen(false);
      setSelectedId(null);
    }
  };

  const confirmDelete = (id: number) => {
    setSelectedId(id);
    setDeleteDialogOpen(true);
  };

  const columns: ColumnDef<DictionaryEntry>[] = [
    {
      accessorKey: 'key',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Key' />
      ),
      cell: ({ row }) => <div className='font-medium'>{row.getValue('key')}</div>,
    },
    {
      accessorKey: 'description',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Description' />
      ),
      cell: ({ row }) => (
        <div className='max-w-[300px] truncate'>
          {row.getValue('description') || 'No description'}
        </div>
      ),
    },
    {
      accessorKey: 'enabled',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Status' />
      ),
      cell: ({ row }) => (
        <Badge variant={row.getValue('enabled') ? 'default' : 'secondary'}>
          {row.getValue('enabled') ? 'Enabled' : 'Disabled'}
        </Badge>
      ),
    },
    {
      accessorKey: 'values',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Values' />
      ),
      cell: ({ row }) => {
        const values = row.getValue('values') as DictionaryValue[];
        return (
          <div className='flex flex-wrap gap-1'>
            {values && values.length > 0 ? (
              values.slice(0, 3).map((value) => (
                <Badge key={value.id} variant='outline'>
                  {value.label || value.value}
                </Badge>
              ))
            ) : (
              <span className='text-muted-foreground text-sm'>No values</span>
            )}
            {values && values.length > 3 && (
              <Badge variant='outline'>+{values.length - 3} more</Badge>
            )}
          </div>
        );
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const entry = row.original;
        return (
          <div className='flex items-center gap-2'>
            <Link href={`/dashboard/dictionary/${entry.id}`}>
              <Button variant='ghost' size='icon'>
                <IconEye className='h-4 w-4' />
              </Button>
            </Link>
            <Link href={`/dashboard/dictionary/${entry.id}`}>
              <Button variant='ghost' size='icon'>
                <IconEdit className='h-4 w-4' />
              </Button>
            </Link>
            <Button
              variant='ghost'
              size='icon'
              onClick={() => confirmDelete(entry.id)}
            >
              <IconTrash className='h-4 w-4' />
            </Button>
          </div>
        );
      },
    },
  ];

  const pageCount = Math.ceil(totalItems / pageSize);

  const { table } = useDataTable({
    data,
    columns,
    pageCount
  });

  return (
    <>
      <DataTable table={table}>
        <DataTableToolbar table={table} />
      </DataTable>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              dictionary entry and all its values.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => selectedId && handleDelete(selectedId)}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
