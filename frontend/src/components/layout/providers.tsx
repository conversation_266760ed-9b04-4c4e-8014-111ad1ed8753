'use client';
import { useTheme } from 'next-themes';
import React from 'react';
import { ActiveThemeProvider } from '../active-theme';
import { AuthProvider } from '@/hooks/use-auth';
import { MenuProvider } from '@/hooks/use-menu';
import { PermissionProvider } from '@/hooks/use-permission';

export default function Providers({
  activeThemeValue,
  children
}: {
  activeThemeValue: string;
  children: React.ReactNode;
}) {
  const { resolvedTheme } = useTheme();

  return (
    <>
      <ActiveThemeProvider initialTheme={activeThemeValue}>
        <AuthProvider>
          <PermissionProvider>
            <MenuProvider>
              {children}
            </MenuProvider>
          </PermissionProvider>
        </AuthProvider>
      </ActiveThemeProvider>
    </>
  );
}
