'use client';

import { ReactNode } from 'react';
import { usePermission } from '@/hooks/use-permission';

interface PermissionGuardProps {
  children: ReactNode;
  resourceCode?: string;
  resourceCodes?: string[];
  requireAll?: boolean;
  fallback?: ReactNode;
}

/**
 * Permission Guard Component
 * Conditionally renders children based on user permissions
 */
export function PermissionGuard({
  children,
  resourceCode,
  resourceCodes,
  requireAll = false,
  fallback = null,
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, isLoading } = usePermission();

  // Show loading state while permissions are being fetched
  if (isLoading) {
    return <>{fallback}</>;
  }

  // Single permission check
  if (resourceCode) {
    return hasPermission(resourceCode) ? <>{children}</> : <>{fallback}</>;
  }

  // Multiple permissions check
  if (resourceCodes && resourceCodes.length > 0) {
    const hasAccess = requireAll 
      ? hasAllPermissions(resourceCodes)
      : hasAnyPermission(resourceCodes);
    
    return hasAccess ? <>{children}</> : <>{fallback}</>;
  }

  // No permission requirements - render children
  return <>{children}</>;
}

/**
 * Higher-order component for permission-based rendering
 */
export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  resourceCode: string,
  fallback?: ReactNode
) {
  return function PermissionWrappedComponent(props: P) {
    return (
      <PermissionGuard resourceCode={resourceCode} fallback={fallback}>
        <Component {...props} />
      </PermissionGuard>
    );
  };
}
