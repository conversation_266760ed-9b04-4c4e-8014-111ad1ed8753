'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { usePermission } from '@/hooks/use-permission';
import { VariantProps } from 'class-variance-authority';
import { buttonVariants } from '@/components/ui/button';

type ButtonProps = React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  };

interface PermissionButtonProps extends ButtonProps {
  resourceCode: string;
  fallback?: React.ReactNode;
}

/**
 * Permission Button Component
 * Renders a button only if user has the required permission
 */
export function PermissionButton({
  resourceCode,
  fallback = null,
  children,
  ...buttonProps
}: PermissionButtonProps) {
  const { hasPermission, isLoading } = usePermission();

  // Don't render anything while loading
  if (isLoading) {
    return <>{fallback}</>;
  }

  // Don't render if no permission
  if (!hasPermission(resourceCode)) {
    return <>{fallback}</>;
  }

  return <Button {...buttonProps}>{children}</Button>;
}
