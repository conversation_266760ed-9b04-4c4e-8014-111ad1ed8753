'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { MenuApi } from '@/api/menu-api';
import { NavItem } from '@/types';
import { Icons } from '@/components/icons';
import { navItems as fallbackNavItems } from '@/constants/data';
import { useAuth } from './use-auth';

// Define the shape of our menu context
interface MenuContextType {
  menuItems: NavItem[];
  isLoading: boolean;
  error: Error | null;
  refreshMenus: () => Promise<void>;
}

// Create the menu context
const MenuContext = createContext<MenuContextType | undefined>(undefined);

// Menu provider props
interface MenuProviderProps {
  children: ReactNode;
}

// Create a singleton instance of MenuApi
const menuApi = new MenuApi();

// Helper function to map backend menu items to frontend NavItem format
const mapMenuToNavItem = (menu: any): NavItem => {
  return {
    title: menu.name,
    url: menu.path || '#',
    icon: mapIconName(menu.icon),
    isActive: false,
    items: menu.children?.map(mapMenuToNavItem) || []
  };
};

// Helper function to map backend icon names to frontend icon components
const mapIconName = (iconName: string | null | undefined): keyof typeof Icons => {
  if (!iconName) return 'logo';

  // Map backend icon names to frontend icon names
  const iconMap: Record<string, keyof typeof Icons> = {
    'home': 'dashboard',
    'settings': 'settings',
    'activity': 'dashboard', // Use dashboard as fallback for activity
    'wrench': 'settings', // Use settings as fallback for wrench
    'user': 'user',
    'users': 'user', // Use user as fallback for users
    'menu': 'dashboard', // Use dashboard as fallback for menu
    'user-check': 'user', // Use user as fallback for user-check
    'server': 'dashboard', // Use dashboard as fallback for server
    'code': 'dashboard', // Use dashboard as fallback for code
    'file-text': 'post' // Use post for file-text
  };

  return iconMap[iconName] || 'logo';
};

// Menu provider component
export function MenuProvider({ children }: MenuProviderProps) {
  const [menuItems, setMenuItems] = useState<NavItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { user, isAuthenticated } = useAuth();

  // Function to fetch menu items from the API
  const fetchMenuItems = async () => {
    setIsLoading(true);
    setError(null);

    try {
      let menuTree;

      // Use permission-filtered menu tree if user is authenticated
      if (isAuthenticated && user) {
        try {
          const response = await fetch('/api/menus/user-tree', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            menuTree = await response.json();
          } else {
            // Fallback to regular menu tree
            menuTree = await menuApi.getMenuTree();
          }
        } catch (permissionError) {
          console.warn('Failed to fetch permission-filtered menus, using regular menu tree:', permissionError);
          menuTree = await menuApi.getMenuTree();
        }
      } else {
        // Use regular menu tree for unauthenticated users
        menuTree = await menuApi.getMenuTree();
      }

      const mappedMenuItems = menuTree.map(mapMenuToNavItem);
      setMenuItems(mappedMenuItems);
    } catch (err) {
      console.error('Failed to fetch menu items:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch menu items'));
      // Use fallback navigation items when API fails
      setMenuItems(fallbackNavItems);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch menu items on mount and when user authentication changes
  useEffect(() => {
    fetchMenuItems();
  }, [isAuthenticated, user]);

  const value = {
    menuItems,
    isLoading,
    error,
    refreshMenus: fetchMenuItems
  };

  return <MenuContext.Provider value={value}>{children}</MenuContext.Provider>;
}

// Custom hook to use menu context
export function useMenu() {
  const context = useContext(MenuContext);
  if (context === undefined) {
    throw new Error('useMenu must be used within a MenuProvider');
  }
  return context;
}
