'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import * as authUtils from '@/utils/auth';

// Define the shape of our auth context
interface AuthContextType {
  user: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  loginWithPassword: (account: string, password: string, rememberMe?: boolean) => Promise<void>;
  loginWithSms: (phone: string, smsCode: string, rememberMe?: boolean) => Promise<void>;
  logout: () => void;
}

// Create the auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Check if user is logged in on mount
  useEffect(() => {
    const checkAuth = async () => {
      if (authUtils.isLoggedIn()) {
        try {
          const userData = await authUtils.getCurrentUser();
          setUser(userData);
        } catch (error) {
          console.error('Failed to get user data:', error);
          authUtils.logout();
        }
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  // Login with password
  const loginWithPassword = async (account: string, password: string, rememberMe = false) => {
    setIsLoading(true);
    try {
      const response = await authUtils.loginWithPassword(account, password, rememberMe);
      setUser(response.user);
    } finally {
      setIsLoading(false);
    }
  };

  // Login with SMS
  const loginWithSms = async (phone: string, smsCode: string, rememberMe = false) => {
    setIsLoading(true);
    try {
      const response = await authUtils.loginWithSms(phone, smsCode, rememberMe);
      setUser(response.user);
    } finally {
      setIsLoading(false);
    }
  };

  // Logout
  const logout = () => {
    authUtils.logout();
    setUser(null);
    router.push('/auth/sign-in');
  };

  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    loginWithPassword,
    loginWithSms,
    logout
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
