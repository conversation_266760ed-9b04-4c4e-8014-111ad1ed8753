'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './use-auth';

// Define the shape of our permission context
interface PermissionContextType {
  permissions: string[];
  isLoading: boolean;
  hasPermission: (resourceCode: string) => boolean;
  hasAnyPermission: (resourceCodes: string[]) => boolean;
  hasAllPermissions: (resourceCodes: string[]) => boolean;
  refreshPermissions: () => Promise<void>;
}

// Create the permission context
const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

// Permission provider props
interface PermissionProviderProps {
  children: ReactNode;
}

// Permission provider component
export function PermissionProvider({ children }: PermissionProviderProps) {
  const [permissions, setPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user, isAuthenticated } = useAuth();

  // Function to fetch user permissions
  const fetchPermissions = async () => {
    if (!isAuthenticated || !user) {
      setPermissions([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/rbac/user/${user.id}/permissions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const userPermissions = await response.json();
        setPermissions(userPermissions);
      } else {
        console.error('Failed to fetch permissions:', response.statusText);
        setPermissions([]);
      }
    } catch (error) {
      console.error('Error fetching permissions:', error);
      setPermissions([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch permissions when user changes
  useEffect(() => {
    fetchPermissions();
  }, [user, isAuthenticated]);

  // Check if user has a specific permission
  const hasPermission = (resourceCode: string): boolean => {
    if (!isAuthenticated || isLoading) return false;
    return permissions.includes(resourceCode);
  };

  // Check if user has any of the specified permissions
  const hasAnyPermission = (resourceCodes: string[]): boolean => {
    if (!isAuthenticated || isLoading) return false;
    return resourceCodes.some(code => permissions.includes(code));
  };

  // Check if user has all of the specified permissions
  const hasAllPermissions = (resourceCodes: string[]): boolean => {
    if (!isAuthenticated || isLoading) return false;
    return resourceCodes.every(code => permissions.includes(code));
  };

  const value = {
    permissions,
    isLoading,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    refreshPermissions: fetchPermissions,
  };

  return <PermissionContext.Provider value={value}>{children}</PermissionContext.Provider>;
}

// Custom hook to use permission context
export function usePermission() {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error('usePermission must be used within a PermissionProvider');
  }
  return context;
}
