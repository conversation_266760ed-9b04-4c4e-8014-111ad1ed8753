'use client';

import { toast as sonnerToast } from 'sonner';

interface ToastProps {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success';
  duration?: number;
}

export function useToast() {
  const toast = ({ title, description, variant = 'default', duration }: ToastProps) => {
    const message = title || description || '';
    const fullMessage = title && description ? `${title}: ${description}` : message;

    switch (variant) {
      case 'destructive':
        return sonnerToast.error(fullMessage, { duration });
      case 'success':
        return sonnerToast.success(fullMessage, { duration });
      default:
        return sonnerToast(fullMessage, { duration });
    }
  };

  return { toast };
}
