'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, ChevronRight, Save, Shield } from 'lucide-react';
import { PermissionGuard } from '@/components/permission/permission-guard';
import { useToast } from '@/hooks/use-toast';

interface Role {
  id: number;
  name: string;
  code: string;
  status: boolean;
}

interface Resource {
  id: number;
  code: string;
  name: string;
  description?: string;
  type: 'menu' | 'button' | 'api' | 'page';
  menuId?: number; // Added missing menuId property
  apiPath?: string;
  apiMethod?: string;
  status: boolean;
  orderNum?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface Menu {
  id: number;
  name: string;
  path?: string;
  icon?: string;
  resourceCode?: string;
  children?: Menu[];
}

interface MenuWithResources extends Menu {
  resources: Resource[];
  children?: MenuWithResources[];
}

interface RolePermission {
  id: number;
  roleId: number;
  menuId?: number;
  resourceId: number;
  granted: boolean;
}

export default function PermissionsPage() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedRoleId, setSelectedRoleId] = useState<number | null>(null);
  const [menuTree, setMenuTree] = useState<MenuWithResources[]>([]);
  const [rolePermissions, setRolePermissions] = useState<RolePermission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState<Set<number>>(new Set());
  const { toast } = useToast();

  // Fetch roles
  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/roles/active', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const rolesData = await response.json();
        setRoles(rolesData);
      }
    } catch (error) {
      console.error('Error fetching roles:', error);
    }
  };

  // Fetch menu tree with resources
  const fetchMenuTreeWithResources = async () => {
    try {
      const [menuResponse, resourceResponse] = await Promise.all([
        fetch('/api/menus/tree', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json',
          },
        }),
        fetch('/api/resources/active', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json',
          },
        }),
      ]);

      if (menuResponse.ok && resourceResponse.ok) {
        const menus = await menuResponse.json();
        const resources = await resourceResponse.json();

        // Group resources by menu
        const resourcesByMenu = resources.reduce((acc: Record<number, Resource[]>, resource: Resource) => {
          if (resource.menuId) {
            if (!acc[resource.menuId]) {
              acc[resource.menuId] = [];
            }
            acc[resource.menuId].push(resource);
          }
          return acc;
        }, {});

        // Attach resources to menus
        const attachResources = (menu: Menu): MenuWithResources => ({
          ...menu,
          resources: resourcesByMenu[menu.id] || [],
          children: menu.children?.map(attachResources),
        });

        const menuTreeWithResources = menus.map(attachResources);
        setMenuTree(menuTreeWithResources);
      }
    } catch (error) {
      console.error('Error fetching menu tree and resources:', error);
    }
  };

  // Fetch role permissions
  const fetchRolePermissions = async (roleId: number) => {
    try {
      const response = await fetch(`/api/rbac/role/${roleId}/permissions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const permissions = await response.json();
        setRolePermissions(permissions);
      }
    } catch (error) {
      console.error('Error fetching role permissions:', error);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      await Promise.all([fetchRoles(), fetchMenuTreeWithResources()]);
      setIsLoading(false);
    };
    loadData();
  }, []);

  useEffect(() => {
    if (selectedRoleId) {
      fetchRolePermissions(selectedRoleId);
    }
  }, [selectedRoleId]);

  // Check if resource is granted
  const isResourceGranted = (resourceId: number): boolean => {
    return rolePermissions.some(
      (permission) => permission.resourceId === resourceId && permission.granted
    );
  };

  // Toggle resource permission
  const toggleResourcePermission = (resourceId: number, menuId?: number) => {
    const isCurrentlyGranted = isResourceGranted(resourceId);

    if (isCurrentlyGranted) {
      // Remove permission
      setRolePermissions(prev =>
        prev.filter(p => p.resourceId !== resourceId)
      );
    } else {
      // Add permission
      const newPermission: RolePermission = {
        id: Date.now(), // Temporary ID
        roleId: selectedRoleId!,
        menuId,
        resourceId,
        granted: true,
      };
      setRolePermissions(prev => [...prev, newPermission]);
    }
  };

  // Toggle menu expansion
  const toggleMenuExpansion = (menuId: number) => {
    setExpandedMenus(prev => {
      const newSet = new Set(prev);
      if (newSet.has(menuId)) {
        newSet.delete(menuId);
      } else {
        newSet.add(menuId);
      }
      return newSet;
    });
  };

  // Save permissions
  const savePermissions = async () => {
    if (!selectedRoleId) return;

    setIsSaving(true);
    try {
      const resourceIds = rolePermissions
        .filter(p => p.granted)
        .map(p => p.resourceId);

      const response = await fetch('/api/rbac/assign-permissions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          roleId: selectedRoleId,
          resourceIds,
        }),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Permissions saved successfully',
        });
        // Refresh permissions
        await fetchRolePermissions(selectedRoleId);
      } else {
        const error = await response.json();
        toast({
          title: 'Error',
          description: error.message || 'Failed to save permissions',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error saving permissions:', error);
      toast({
        title: 'Error',
        description: 'Failed to save permissions',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Render menu item with resources
  const renderMenuItem = (menu: MenuWithResources, level: number = 0) => {
    const isExpanded = expandedMenus.has(menu.id);
    const hasChildren = menu.children && menu.children.length > 0;
    const hasResources = menu.resources && menu.resources.length > 0;

    return (
      <div key={menu.id} className={`ml-${level * 4}`}>
        <Collapsible open={isExpanded} onOpenChange={() => toggleMenuExpansion(menu.id)}>
          <div className="flex items-center space-x-2 py-2">
            {(hasChildren || hasResources) && (
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
            )}
            <div className="flex items-center space-x-2">
              <span className="font-medium">{menu.name}</span>
              {menu.resourceCode && (
                <Badge variant="outline" className="text-xs">
                  {menu.resourceCode}
                </Badge>
              )}
            </div>
          </div>

          <CollapsibleContent>
            {/* Render resources for this menu */}
            {hasResources && (
              <div className="ml-6 space-y-2">
                {menu.resources.map((resource) => (
                  <div key={resource.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`resource-${resource.id}`}
                      checked={isResourceGranted(resource.id)}
                      onCheckedChange={() => toggleResourcePermission(resource.id, menu.id)}
                      disabled={!selectedRoleId}
                    />
                    <label
                      htmlFor={`resource-${resource.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {resource.name}
                    </label>
                    <Badge variant="secondary" className="text-xs">
                      {resource.type}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {resource.code}
                    </Badge>
                  </div>
                ))}
              </div>
            )}

            {/* Render child menus */}
            {hasChildren && (
              <div className="ml-4">
                {menu.children!.map((child) => renderMenuItem(child, level + 1))}
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </div>
    );
  };

  return (
    <PermissionGuard resourceCode="PERMISSION_MANAGEMENT">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Permission Management</h1>
            <p className="text-muted-foreground">
              Assign permissions to roles by selecting menus and resources
            </p>
          </div>
          <Button
            onClick={savePermissions}
            disabled={!selectedRoleId || isSaving}
          >
            <Save className="mr-2 h-4 w-4" />
            {isSaving ? 'Saving...' : 'Save Permissions'}
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="mr-2 h-5 w-5" />
              Role Selection
            </CardTitle>
            <CardDescription>
              Select a role to manage its permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select
              value={selectedRoleId?.toString() || ''}
              onValueChange={(value) => setSelectedRoleId(parseInt(value))}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                {roles.map((role) => (
                  <SelectItem key={role.id} value={role.id.toString()}>
                    {role.name} ({role.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {selectedRoleId && (
          <Card>
            <CardHeader>
              <CardTitle>Menu & Resource Permissions</CardTitle>
              <CardDescription>
                Check the boxes to grant permissions for menus and resources
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : (
                <div className="space-y-2">
                  {menuTree.map((menu) => renderMenuItem(menu))}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </PermissionGuard>
  );
}
