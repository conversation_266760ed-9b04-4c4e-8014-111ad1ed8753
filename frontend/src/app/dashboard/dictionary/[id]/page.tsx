import PageContainer from '@/components/layout/page-container';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import DictionaryDetailPage from '@/features/dictionary/components/dictionary-detail';
import { Suspense } from 'react';

export const metadata = {
  title: 'Dictionary Detail'
};

import { use } from 'react';

interface PageParams {
  id: string;
}

interface PageProps {
  params: Promise<PageParams>;
}

export default function DictionaryDetailRoute(props: PageProps) {
  const params = use(props.params);
  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-4'>
        <Heading
          title='Dictionary Detail'
          description='View and edit dictionary entry details'
        />
        <Separator />
        <Suspense fallback={<div>Loading...</div>}>
          <DictionaryDetailPage id={params.id} />
        </Suspense>
      </div>
    </PageContainer>
  );
}
