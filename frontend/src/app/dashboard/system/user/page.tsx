import PageContainer from '@/components/layout/page-container';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { DataTableSkeleton } from '@/components/ui/table/data-table-skeleton';
import UserManagementContent from '@/features/user/components/user-management-content';
import { Suspense } from 'react';

export const metadata = {
  title: 'Dashboard: User Management'
};

export default function UserManagementPage() {
  return (
    <PageContainer scrollable={false}>
      <div className='flex flex-1 flex-col space-y-4'>
        <div className='flex items-start justify-between'>
          <Heading
            title='User Management'
            description='Manage system users and their access permissions'
          />
        </div>
        <Separator />
        <Suspense
          fallback={
            <DataTableSkeleton columnCount={6} rowCount={8} filterCount={2} />
          }
        >
          <UserManagementContent />
        </Suspense>
      </div>
    </PageContainer>
  );
}
