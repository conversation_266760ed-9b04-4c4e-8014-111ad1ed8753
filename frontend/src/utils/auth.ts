import { AuthApi } from '../api/auth-api';
import { AuthType } from '../api/auth-types';

// Define AuthResponse interface
interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: any;
}

// Create a singleton instance of AuthApi
const authApi = new AuthApi();

/**
 * Login with password
 * @param account Username, email or phone
 * @param password User password
 * @param rememberMe Whether to remember the user
 * @returns Authentication response
 */
export async function loginWithPassword(account: string, password: string, rememberMe: boolean = false): Promise<AuthResponse> {
  try {
    const response = await authApi.login({
      account,
      authCredential: password,
      authType: AuthType.PASSWORD,
      rememberMe
    });

    // Store tokens in localStorage
    if (response) {
      localStorage.setItem('accessToken', response.accessToken);
      localStorage.setItem('refreshToken', response.refreshToken);
    }

    return response;
  } catch (error) {
    console.error('Password login failed:', error);
    throw error;
  }
}

/**
 * Login with SMS verification code
 * @param phone Phone number
 * @param smsCode SMS verification code
 * @param rememberMe Whether to remember the user
 * @returns Authentication response
 */
export async function loginWithSms(phone: string, smsCode: string, rememberMe: boolean = false): Promise<AuthResponse> {
  try {
    const response = await authApi.login({
      account: phone,
      authCredential: smsCode,
      authType: AuthType.SMS_VERIFICATION,
      rememberMe
    });

    // Store tokens in localStorage
    if (response) {
      localStorage.setItem('accessToken', response.accessToken);
      localStorage.setItem('refreshToken', response.refreshToken);
    }

    return response;
  } catch (error) {
    console.error('SMS login failed:', error);
    throw error;
  }
}

/**
 * Refresh access token
 * @param refreshToken Refresh token
 * @returns New authentication response
 */
export async function refreshAccessToken(refreshToken: string): Promise<AuthResponse> {
  try {
    const response = await authApi.refreshToken({
      refreshToken,
      token: localStorage.getItem('accessToken') || ''
    });

    // Update tokens in localStorage
    if (response) {
      localStorage.setItem('accessToken', response.accessToken);
      localStorage.setItem('refreshToken', response.refreshToken);
    }

    return response;
  } catch (error) {
    console.error('Token refresh failed:', error);
    // Clear tokens on refresh failure
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    throw error;
  }
}

/**
 * Get current user information
 * @returns User information
 */
export async function getCurrentUser(): Promise<any> {
  try {
    return await authApi.getCurrentUser();
  } catch (error) {
    console.error('Failed to get current user:', error);
    throw error;
  }
}

/**
 * Check if user is logged in
 * @returns True if user is logged in
 */
export function isLoggedIn(): boolean {
  return !!localStorage.getItem('accessToken');
}

/**
 * Logout user
 */
export function logout(): void {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
}
