import {
  Inter,
  <PERSON><PERSON>,
  <PERSON><PERSON>_Sans_Mono,
  <PERSON><PERSON>_Mono
} from 'next/font/google';

import localFont from 'next/font/local';

import { cn } from '@/lib/utils';

// Use Inter as a replacement for Geist
const fontSans = Inter({
  subsets: ['latin'],
  variable: '--font-sans'
});

// Use Noto Sans Mono as a replacement for Geist Mono
const fontMono = Noto_Sans_Mono({
  subsets: ['latin'],
  variable: '--font-mono'
});

// Use Roboto Mono as a replacement for Instrument Sans
const fontInstrument = Roboto_Mono({
  subsets: ['latin'],
  weight: ['400', '500', '700'],
  variable: '--font-instrument'
});

const fontNotoMono = Noto_Sans_Mono({
  subsets: ['latin'],
  variable: '--font-noto-mono'
});

const fontMullish = Mulish({
  subsets: ['latin'],
  variable: '--font-mullish'
});

const fontInter = Inter({
  subsets: ['latin'],
  variable: '--font-inter'
});

export const fontVariables = cn(
  fontSans.variable,
  fontMono.variable,
  fontInstrument.variable,
  fontNotoMono.variable,
  fontMullish.variable,
  fontInter.variable
);
