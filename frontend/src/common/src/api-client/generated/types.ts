// This file is auto-generated by generate-api-client-no-schemas.js
// It exports all types from common/src that are used in the API client

// Define types that are not in the project but are in the OpenAPI spec
export interface SendSmsDto {
  phone?: string;
  captchaToken?: string;
  captchaText?: string;
}

export interface VerifySmsDto {
  phone?: string;
  code?: string;
}

// API 响应类型
export interface CaptchaResponseData {
  token: string;
  svg: string;
}

export interface CaptchaResponse {
  success: boolean;
  data: CaptchaResponseData;
}

export interface SmsResponse {
  success: boolean;
  message: string;
}

export * as ApiTypes from './types';
