import axios from 'axios';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined' && typeof localStorage !== 'undefined';

// Create axios instance
const AXIOS_INSTANCE = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include auth token
AXIOS_INSTANCE.interceptors.request.use(
  (config) => {
    // Only get token in browser environment
    if (isBrowser) {
      const token = localStorage.getItem('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor for error handling
AXIOS_INSTANCE.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle 401 errors (unauthorized) - could implement token refresh here
    if (error.response?.status === 401 && isBrowser) {
      // Clear tokens if unauthorized (only in browser)
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
    }
    return Promise.reject(error);
  }
);

// Custom instance for API calls
const customInstance = (config) => {
  const source = axios.CancelToken.source();
  const promise = AXIOS_INSTANCE({
    ...config,
    cancelToken: source.token,
  }).then(({ data }) => data);

  promise.cancel = () => {
    source.cancel('Query was cancelled');
  };

  return promise;
};

// Menu API class
export class MenuApi {
  constructor(client = customInstance) {
    this.client = client;
  }

  /**
   * Get all menus
   */
  getAllMenus() {
    return this.client({
      url: `/menus`,
      method: 'get',
    });
  }

  /**
   * Get menu tree
   */
  getMenuTree() {
    return this.client({
      url: `/menus/tree`,
      method: 'get',
    });
  }

  /**
   * Get menu by id
   */
  getMenuById(id) {
    return this.client({
      url: `/menus/${id}`,
      method: 'get',
    });
  }

  /**
   * Create a new menu
   */
  createMenu(menuData) {
    return this.client({
      url: `/menus`,
      method: 'post',
      data: menuData,
    });
  }

  /**
   * Update a menu
   */
  updateMenu(id, menuData) {
    return this.client({
      url: `/menus/${id}`,
      method: 'put',
      data: menuData,
    });
  }

  /**
   * Delete a menu
   */
  deleteMenu(id) {
    return this.client({
      url: `/menus/${id}`,
      method: 'delete',
    });
  }
}
