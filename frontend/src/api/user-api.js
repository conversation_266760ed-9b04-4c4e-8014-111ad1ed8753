import axios from 'axios';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined' && typeof localStorage !== 'undefined';

// Create axios instance
const AXIOS_INSTANCE = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include auth token
AXIOS_INSTANCE.interceptors.request.use(
  (config) => {
    // Only get token in browser environment
    if (isBrowser) {
      const token = localStorage.getItem('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor for error handling
AXIOS_INSTANCE.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle 401 errors (unauthorized) - implement token refresh
    if (error.response?.status === 401 && isBrowser) {
      const refreshToken = localStorage.getItem('refreshToken');
      const originalRequest = error.config;

      // Only try to refresh if we have a refresh token and haven't tried already
      if (refreshToken && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          const response = await axios.post('/api/auth/refresh-token', {
            refreshToken,
          });

          const { accessToken } = response.data;
          localStorage.setItem('accessToken', accessToken);

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return AXIOS_INSTANCE(originalRequest);
        } catch (refreshError) {
          // Refresh failed, redirect to login
          if (isBrowser) {
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            window.location.href = '/auth/sign-in';
          }
          return Promise.reject(refreshError);
        }
      } else {
        // No refresh token or already tried, redirect to login
        if (isBrowser) {
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          window.location.href = '/auth/sign-in';
        }
      }
    }

    return Promise.reject(error);
  }
);

// Custom instance for API calls
const customInstance = (config) => {
  const source = axios.CancelToken.source();
  const promise = AXIOS_INSTANCE({
    ...config,
    cancelToken: source.token,
  }).then(({ data }) => data);

  promise.cancel = () => {
    source.cancel('Query was cancelled');
  };

  return promise;
};

// User API class
export class UserApi {
  constructor(client = customInstance) {
    this.client = client;
  }

  /**
   * Get users with filtering and pagination
   */
  getUsers(params = {}) {
    return this.client({
      url: `/users`,
      method: 'get',
      params,
    });
  }

  /**
   * Get user by ID
   */
  getUserById(id) {
    return this.client({
      url: `/users/${id}`,
      method: 'get',
    });
  }

  /**
   * Update user status (enable/disable)
   */
  updateUserStatus(id, status) {
    return this.client({
      url: `/users/${id}/status`,
      method: 'patch',
      data: { status },
    });
  }
}

// Export default instance
export const userApi = new UserApi();
