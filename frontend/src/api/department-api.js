import axios from 'axios';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined' && typeof localStorage !== 'undefined';

// Create axios instance
const AXIOS_INSTANCE = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include auth token
AXIOS_INSTANCE.interceptors.request.use(
  (config) => {
    // Only get token in browser environment
    if (isBrowser) {
      const token = localStorage.getItem('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor for error handling
AXIOS_INSTANCE.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle 401 errors (unauthorized) - could implement token refresh here
    if (error.response?.status === 401 && isBrowser) {
      // Clear tokens if unauthorized (only in browser)
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
    }
    return Promise.reject(error);
  }
);

// Custom instance for API calls
const customInstance = (config) => {
  const source = axios.CancelToken.source();
  const promise = AXIOS_INSTANCE({
    ...config,
    cancelToken: source.token,
  }).then(({ data }) => data);

  promise.cancel = () => {
    source.cancel('Query was cancelled');
  };

  return promise;
};

// Department API class
export class DepartmentApi {
  constructor(client = customInstance) {
    this.client = client;
  }

  /**
   * Get all departments
   */
  getAllDepartments(query = {}) {
    return this.client({
      url: `/departments`,
      method: 'get',
      params: query,
    });
  }

  /**
   * Get department tree
   */
  getDepartmentTree() {
    return this.client({
      url: `/departments/tree`,
      method: 'get',
    });
  }

  /**
   * Get department by id
   */
  getDepartmentById(id) {
    return this.client({
      url: `/departments/${id}`,
      method: 'get',
    });
  }

  /**
   * Create a new department
   */
  createDepartment(departmentData) {
    return this.client({
      url: `/departments`,
      method: 'post',
      data: departmentData,
    });
  }

  /**
   * Update a department
   */
  updateDepartment(id, departmentData) {
    return this.client({
      url: `/departments/${id}`,
      method: 'put',
      data: departmentData,
    });
  }

  /**
   * Delete a department
   */
  deleteDepartment(id) {
    return this.client({
      url: `/departments/${id}`,
      method: 'delete',
    });
  }
}
