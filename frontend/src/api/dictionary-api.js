import { customInstance } from '../../../common/src/api-client/mutator/custom-instance';

// Dictionary API class
export class DictionaryApi {
  constructor(client = customInstance) {
    this.client = client;
  }

  /**
   * Get all dictionary entries
   */
  getAllDictionaries(query = {}) {
    return this.client({
      url: `/dictionary`,
      method: 'get',
      params: query,
    });
  }

  /**
   * Get dictionary entry by id
   */
  getDictionaryById(id) {
    return this.client({
      url: `/dictionary/${id}`,
      method: 'get',
    });
  }

  /**
   * Get dictionary entry by key
   */
  getDictionaryByKey(key) {
    return this.client({
      url: `/dictionary/key/${key}`,
      method: 'get',
    });
  }

  /**
   * Create a new dictionary entry
   */
  createDictionary(dictionaryData) {
    return this.client({
      url: `/dictionary`,
      method: 'post',
      data: dictionaryData,
    });
  }

  /**
   * Update a dictionary entry
   */
  updateDictionary(id, dictionaryData) {
    return this.client({
      url: `/dictionary/${id}`,
      method: 'put',
      data: dictionaryData,
    });
  }

  /**
   * Delete a dictionary entry
   */
  deleteDictionary(id) {
    return this.client({
      url: `/dictionary/${id}`,
      method: 'delete',
    });
  }

  /**
   * Add a value to a dictionary entry
   */
  addDictionaryValue(id, valueData) {
    return this.client({
      url: `/dictionary/${id}/values`,
      method: 'post',
      data: valueData,
    });
  }

  /**
   * Delete a dictionary value
   */
  deleteDictionaryValue(id) {
    return this.client({
      url: `/dictionary/values/${id}`,
      method: 'delete',
    });
  }
}
