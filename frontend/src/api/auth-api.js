import axios from 'axios';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined' && typeof localStorage !== 'undefined';

// Create axios instance
const AXIOS_INSTANCE = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include auth token
AXIOS_INSTANCE.interceptors.request.use(
  (config) => {
    // Only get token in browser environment
    if (isBrowser) {
      const token = localStorage.getItem('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor for error handling
AXIOS_INSTANCE.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle 401 errors (unauthorized) - implement token refresh
    if (error.response?.status === 401 && isBrowser) {
      const refreshToken = localStorage.getItem('refreshToken');
      const originalRequest = error.config;

      // Only try to refresh if we have a refresh token and haven't tried already
      if (refreshToken && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          // Create a new instance to avoid interceptor loop
          const response = await axios.post('/api/auth/refresh-token', {
            refreshToken,
            token: localStorage.getItem('accessToken') || ''
          });

          if (response.data) {
            // Update tokens in localStorage
            localStorage.setItem('accessToken', response.data.accessToken);
            localStorage.setItem('refreshToken', response.data.refreshToken);

            // Update the Authorization header
            AXIOS_INSTANCE.defaults.headers.common['Authorization'] = `Bearer ${response.data.accessToken}`;
            originalRequest.headers['Authorization'] = `Bearer ${response.data.accessToken}`;

            // Retry the original request
            return AXIOS_INSTANCE(originalRequest);
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          // Clear tokens on refresh failure
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');

          // Redirect to login page
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/sign-in';
          }
        }
      } else {
        // No refresh token or already tried refreshing
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');

        // Redirect to login page if not already there
        if (typeof window !== 'undefined' && !window.location.pathname.includes('/auth/sign-in')) {
          window.location.href = '/auth/sign-in';
        }
      }
    }
    return Promise.reject(error);
  }
);

// Custom instance for API calls
const customInstance = (config) => {
  const source = axios.CancelToken.source();
  const promise = AXIOS_INSTANCE({
    ...config,
    cancelToken: source.token,
  }).then(({ data }) => data);

  promise.cancel = () => {
    source.cancel('Query was cancelled');
  };

  return promise;
};

// Auth API class
export class AuthApi {
  constructor(client = customInstance) {
    this.client = client;
  }

  /**
   * Login with credentials
   */
  login(loginDto) {
    return this.client({
      url: `/auth/login`,
      method: 'post',
      data: loginDto,
    });
  }

  /**
   * Refresh token
   */
  refreshToken(refreshTokenDto) {
    return this.client({
      url: `/auth/refresh-token`,
      method: 'post',
      data: refreshTokenDto,
    });
  }

  /**
   * Get current user information
   */
  getCurrentUser() {
    return this.client({
      url: `/auth/me`,
      method: 'get',
    });
  }
}

// SMS API class
export class SmsApi {
  constructor(client = customInstance) {
    this.client = client;
  }

  /**
   * Get captcha
   */
  getCaptcha() {
    return this.client({
      url: `/sms/captcha`,
      method: 'get',
    });
  }

  /**
   * Send SMS code
   */
  sendSmsCode(sendSmsDto) {
    return this.client({
      url: `/sms/send`,
      method: 'post',
      data: sendSmsDto,
    });
  }

  /**
   * Verify SMS code
   */
  verifySmsCode(verifySmsDto) {
    return this.client({
      url: `/sms/verify`,
      method: 'post',
      data: verifySmsDto,
    });
  }
}
